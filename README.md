# Environment Setup

## Setting up environment variables

1. Copy `.env.template` to create your `.env` file:
   ```bash
   cp .env.template .env
   ```

2. Update the `.env` file with your actual values:
   - Generate a new SECRET_KEY for Django
   - Set up your database credentials
   - Configure your email settings
   - Add your domain to ALLOWED_HOSTS
   - Set your SITE_URL
   - Add your GEMINI_API_KEY

3. For development:
   - Set `DEBUG=True`
   - Use `localhost` or `127.0.0.1` in ALLOWED_HOSTS
   - You can use SQLite instead of PostgreSQL by updating database settings

4. For production:
   - Set `DEBUG=False`
   - Use your actual domain in ALLOWED_HOSTS and SITE_URL
   - Use strong passwords and secure credentials
   - Use environment-specific email settings
   - Enable proper security measures

## Email Setup with Gmail

1. Create an App Password:
   - Go to your Google Account settings
   - Navigate to Security > 2-Step Verification
   - Scroll down and select "App passwords"
   - Generate a new app password for your application
   - Use this password in EMAIL_HOST_PASSWORD

2. Update email settings in .env:
   ```
   EMAIL_HOST_USER=<EMAIL>
   EMAIL_HOST_PASSWORD=your-app-specific-password
   DEFAULT_FROM_EMAIL=Your Name <<EMAIL>>
   ```

## Important Notes

- Never commit the `.env` file to version control
- Keep your production credentials secure
- Regularly rotate sensitive credentials
- Monitor your application logs for security issues
- Keep your dependencies updated 