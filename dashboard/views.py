from django.contrib.auth.decorators import login_required
from django.db.models import Count
from django.shortcuts import render, redirect
from .models import JobApplication, JobListing

@login_required
def dashboard(request):
    if request.user.is_jobseeker():
        # Get recent applications
        recent_applications = JobApplication.objects.filter(
            applicant=request.user
        ).order_by('-applied_at')[:5]
        
        # Get pending video interviews
        pending_interviews = JobApplication.objects.filter(
            applicant=request.user,
            video_interview_status=JobApplication.VIDEO_STATUS_PENDING
        ).select_related('job', 'job__employer').order_by('-applied_at')
        
        context = {
            'recent_applications': recent_applications,
            'pending_interviews': pending_interviews,
        }
        return render(request, 'dashboard/jobseeker_dashboard.html', context)
    
    elif request.user.is_employer():
        # Get employer's active jobs
        active_jobs = JobListing.objects.filter(
            employer=request.user,
            is_active=True
        ).annotate(
            applications_count=Count('jobapplication')
        ).order_by('-created_at')[:5]
        
        # Get recent applications for employer's jobs
        recent_applications = JobApplication.objects.filter(
            job__employer=request.user
        ).select_related(
            'applicant', 'job'
        ).order_by('-applied_at')[:5]
        
        context = {
            'active_jobs': active_jobs,
            'recent_applications': recent_applications,
        }
        return render(request, 'dashboard/employer_dashboard.html', context)
    
    return redirect('home') 