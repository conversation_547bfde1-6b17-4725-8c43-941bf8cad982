from django import forms
from .models import InterviewQuestion, VideoResponse
from jobs.models import JobListing

class InterviewQuestionForm(forms.ModelForm):
    question = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Enter your interview question here'
        }),
        required=True,
        error_messages={
            'required': 'Please enter a question'
        }
    )
    
    class Meta:
        model = InterviewQuestion
        fields = ['question']

InterviewQuestionFormSet = forms.inlineformset_factory(
    JobListing,
    InterviewQuestion,
    form=InterviewQuestionForm,
    extra=1,
    min_num=1,
    max_num=5,
    validate_min=True,
    can_delete=True,
    validate_max=True,
    absolute_max=5,
    fields=['question'],
)

class VideoResponseForm(forms.ModelForm):
    class Meta:
        model = VideoResponse
        fields = ['video_file']

    def clean_max_duration(self):
        duration = self.cleaned_data.get('max_duration')
        if duration:
            if duration < 30:
                raise forms.ValidationError("Duration must be at least 30 seconds")
            if duration > 300:
                raise forms.ValidationError("Duration cannot exceed 300 seconds")
        return duration 