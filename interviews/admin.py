from django.contrib import admin
from .models import Interview<PERSON>uestion, VideoResponse, InterviewRecording, AIPrompt
from django.utils.html import format_html
from django import forms

@admin.register(InterviewQuestion)
class InterviewQuestionAdmin(admin.ModelAdmin):
    list_display = ('job', 'question', 'order')
    list_filter = ('job',)
    ordering = ('job', 'order')

@admin.register(VideoResponse)
class VideoResponseAdmin(admin.ModelAdmin):
    list_display = ('application', 'created_at')
    list_filter = ('application__job', 'created_at')
    readonly_fields = ('created_at',)

class AIPromptAdminForm(forms.ModelForm):
    # Add a choice field for default models
    default_models = forms.ChoiceField(
        choices=[('', '-- Custom Model --')] + AIPrompt.DEFAULT_MODELS,
        required=False,
        help_text="Select a default model or enter a custom one below"
    )

    class Meta:
        model = AIPrompt
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Set default model if it matches
            if self.instance.model_name in dict(AIPrompt.DEFAULT_MODELS):
                self.fields['default_models'].initial = self.instance.model_name

    def clean(self):
        cleaned_data = super().clean()
        default_model = cleaned_data.get('default_models')
        custom_model = cleaned_data.get('model_name')

        # Use default model if selected, otherwise use custom input
        if default_model:
            cleaned_data['model_name'] = default_model
            cleaned_data['model_display_name'] = dict(AIPrompt.DEFAULT_MODELS)[default_model]
        elif not custom_model:
            raise forms.ValidationError("Please either select a default model or enter a custom one.")

        return cleaned_data

@admin.register(AIPrompt)
class AIPromptAdmin(admin.ModelAdmin):
    form = AIPromptAdminForm
    list_display = ('name', 'model_display_name', 'is_active', 'created_at')
    list_filter = ('is_active', 'model_display_name')
    search_fields = ('name', 'prompt_text', 'model_name', 'model_display_name')
    fieldsets = (
        (None, {
            'fields': ('name', 'is_active')
        }),
        ('Model Configuration', {
            'fields': ('default_models', 'model_name', 'model_display_name'),
            'description': 'Select a default model or enter a custom one'
        }),
        ('Prompt Configuration', {
            'fields': ('prompt_text',),
            'description': 'Enter your prompt. Use {questions} where you want the interview questions to appear.'
        }),
    )

    def save_model(self, request, obj, form, change):
        if obj.is_active:
            AIPrompt.objects.exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

@admin.register(InterviewRecording)
class InterviewRecordingAdmin(admin.ModelAdmin):
    list_display = ('application', 'question', 'question_number', 'created_at')
    list_filter = ('application__job', 'created_at')
    search_fields = ('application__applicant__email', 'question__question')
    readonly_fields = ('ai_analysis',)

    def view_analysis(self, obj):
        if obj.ai_analysis:
            return format_html('<pre>{}</pre>', obj.ai_analysis)
        return "No analysis available"
    
    view_analysis.short_description = "AI Analysis" 