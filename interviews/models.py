from django.db import models
from django.conf import settings
from jobs.models import JobApplication
from django.core.exceptions import ValidationError
import os
from django.db.models.signals import pre_delete
from django.dispatch import receiver
import logging

logger = logging.getLogger(__name__)

class InterviewQuestion(models.Model):
    job = models.ForeignKey('jobs.JobListing', on_delete=models.CASCADE, related_name='interview_questions')
    question = models.TextField()
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']
        unique_together = ['job', 'order']

    def __str__(self):
        return f"Question {self.order} for {self.job.title}"

    def save(self, *args, **kwargs):
        if not self.order and self.job:
            # Auto-increment order if not set
            last_order = InterviewQuestion.objects.filter(job=self.job).aggregate(
                models.Max('order'))['order__max'] or 0
            self.order = last_order + 1
        super().save(*args, **kwargs)

def validate_video_file(value):
    ext = os.path.splitext(value.name)[1]
    valid_extensions = ['.mp4', '.webm', '.mov']
    if not ext.lower() in valid_extensions:
        raise ValidationError('Unsupported file extension.')

class VideoResponse(models.Model):
    application = models.ForeignKey(JobApplication, on_delete=models.CASCADE)
    video_file = models.FileField(
        upload_to='video_responses/',
        validators=[validate_video_file]
    )
    created_at = models.DateTimeField(auto_now_add=True)
    feedback = models.TextField(blank=True)
    ai_analysis = models.JSONField(null=True, blank=True)
    analysis_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('failed', 'Failed')
        ],
        default='pending'
    )

    def __str__(self):
        return f"Interview recording for {self.application.applicant.username}"

    def analyze_with_gemini(self):
        try:
            # Placeholder for video analysis
            self.ai_analysis = {
                'status': 'success',
                'analysis': {
                    'confidence': 'The candidate appears confident in their responses.',
                    'communication': 'Clear and articulate communication style.',
                    'appearance': 'Professional appearance and demeanor.',
                    'overall': 'Strong candidate with good potential.',
                    'improvements': 'Could improve on technical specifics.'
                }
            }
            self.save()
            
        except Exception as e:
            logger.error(f"Video analysis failed: {str(e)}")

class AIPrompt(models.Model):
    # Default models as choices
    DEFAULT_MODELS = [
        ('gemini-2.0-flash-exp', 'Gemini Flash (Default)'),
        ('gemini-pro', 'Gemini Pro'),
        ('gemini-pro-vision', 'Gemini Pro Vision'),
    ]

    name = models.CharField(max_length=100)
    model_name = models.CharField(
        max_length=100,
        default='gemini-2.0-flash-exp',  # Default to latest model
        help_text="Enter the model identifier (e.g., gemini-2.0-flash-exp). You can use default models or enter a new one."
    )
    model_display_name = models.CharField(
        max_length=100,
        default='gemini-2.0-flash-exp',  # Match the model_name default
        help_text="Display name for the model (e.g., 'Gemini Flash 2.0')"
    )
    prompt_text = models.TextField(
        help_text="Enter your prompt. Must include {questions} placeholder for interview questions."
    )
    is_active = models.BooleanField(
        default=False,
        help_text="Only one prompt can be active at a time."
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.model_display_name}) - {'Active' if self.is_active else 'Inactive'}"

    @classmethod
    def get_active_prompt(cls):
        """Get the currently active prompt or return None"""
        return cls.objects.filter(is_active=True).first()

class InterviewRecording(models.Model):
    application = models.ForeignKey(JobApplication, on_delete=models.CASCADE)
    question = models.ForeignKey(InterviewQuestion, on_delete=models.CASCADE)
    question_number = models.IntegerField()
    video_file = models.FileField(upload_to='interview_recordings/')
    created_at = models.DateTimeField(auto_now_add=True)
    ai_analysis = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['question_number']

    def __str__(self):
        return f"Recording {self.question_number} - {self.application.applicant.get_full_name()}"

    def analyze_response(self):
        """
        Placeholder for video analysis implementation.
        You can implement your custom video analysis logic here.
        """
        try:
            # Your video analysis implementation will go here
            pass
            
        except Exception as e:
            print(f"Error analyzing video: {str(e)}")
            return None 

@receiver(pre_delete, sender=VideoResponse)
def video_response_delete(sender, instance, **kwargs):
    if instance.video_file:
        if os.path.isfile(instance.video_file.path):
            os.remove(instance.video_file.path)

class Interview(models.Model):
    ANALYSIS_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]
    
    MAX_ATTEMPTS = 3  # Maximum number of recordings allowed
    
    application = models.ForeignKey(JobApplication, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    analysis_status = models.CharField(
        max_length=20,
        choices=ANALYSIS_STATUS_CHOICES,
        default='pending'
    )
    ai_analysis = models.TextField(null=True, blank=True)
    analysis_error = models.TextField(null=True, blank=True)
    ai_score = models.DecimalField(
        max_digits=3,
        decimal_places=1,
        null=True,
        blank=True,
        help_text="AI-generated interview score out of 10"
    )

    def __str__(self):
        return f"Interview for {self.application}"

    def get_questions(self):
        return self.application.job.interview_questions.all().order_by('order')

    def get_status_color(self):
        status_colors = {
            'pending': 'warning',
            'processing': 'info',
            'completed': 'success',
            'failed': 'danger'
        }
        return status_colors.get(self.analysis_status, 'secondary')

    def delete(self, *args, **kwargs):
        if self.video_file:
            try:
                self.video_file.delete()
            except Exception as e:
                logger.error(f"Error deleting video file: {e}")
        super().delete(*args, **kwargs)

class VideoAttempt(models.Model):
    interview = models.ForeignKey(Interview, on_delete=models.CASCADE, related_name='video_attempts')
    video_file = models.FileField(
        upload_to='interview_videos/',
        validators=[validate_video_file]
    )
    is_selected = models.BooleanField(default=False)  # Indicates if this is the chosen attempt
    created_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if self.is_selected:
            # Ensure only one video is selected
            self.interview.video_attempts.exclude(pk=self.pk).update(is_selected=False)
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if self.video_file:
            try:
                self.video_file.delete()
            except Exception as e:
                logger.error(f"Error deleting video file: {e}")
        super().delete(*args, **kwargs) 