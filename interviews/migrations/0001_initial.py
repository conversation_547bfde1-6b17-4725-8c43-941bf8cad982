# Generated by Django 4.2.21 on 2025-06-30 10:00

from django.db import migrations, models
import django.db.models.deletion
import interviews.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("jobs", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AIPrompt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "model_name",
                    models.CharField(
                        default="gemini-2.0-flash-exp",
                        help_text="Enter the model identifier (e.g., gemini-2.0-flash-exp). You can use default models or enter a new one.",
                        max_length=100,
                    ),
                ),
                (
                    "model_display_name",
                    models.CharField(
                        default="gemini-2.0-flash-exp",
                        help_text="Display name for the model (e.g., 'Gemini Flash 2.0')",
                        max_length=100,
                    ),
                ),
                (
                    "prompt_text",
                    models.TextField(
                        help_text="Enter your prompt. Must include {questions} placeholder for interview questions."
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=False,
                        help_text="Only one prompt can be active at a time.",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Interview",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "analysis_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("ai_analysis", models.TextField(blank=True, null=True)),
                ("analysis_error", models.TextField(blank=True, null=True)),
                (
                    "ai_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        help_text="AI-generated interview score out of 10",
                        max_digits=3,
                        null=True,
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jobs.jobapplication",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="InterviewQuestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question", models.TextField()),
                ("order", models.PositiveIntegerField(default=0)),
                (
                    "job",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="interview_questions",
                        to="jobs.joblisting",
                    ),
                ),
            ],
            options={
                "ordering": ["order"],
                "unique_together": {("job", "order")},
            },
        ),
        migrations.CreateModel(
            name="VideoResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "video_file",
                    models.FileField(
                        upload_to="video_responses/",
                        validators=[interviews.models.validate_video_file],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("feedback", models.TextField(blank=True)),
                ("ai_analysis", models.JSONField(blank=True, null=True)),
                (
                    "analysis_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jobs.jobapplication",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="VideoAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "video_file",
                    models.FileField(
                        upload_to="interview_videos/",
                        validators=[interviews.models.validate_video_file],
                    ),
                ),
                ("is_selected", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "interview",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="video_attempts",
                        to="interviews.interview",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="InterviewRecording",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question_number", models.IntegerField()),
                ("video_file", models.FileField(upload_to="interview_recordings/")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("ai_analysis", models.TextField(blank=True, null=True)),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jobs.jobapplication",
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="interviews.interviewquestion",
                    ),
                ),
            ],
            options={
                "ordering": ["question_number"],
            },
        ),
    ]
