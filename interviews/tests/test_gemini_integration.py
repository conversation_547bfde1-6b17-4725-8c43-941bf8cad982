from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from interviews.models import Interview, InterviewQuestion
from jobs.models import JobApplication, JobListing
from accounts.models import CustomUser
from interviews.services.gemini_service import GeminiAnalysisService
import os

class GeminiIntegrationTest(TestCase):
    def setUp(self):
        # Create test user
        self.user = CustomUser.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test job
        self.job = JobListing.objects.create(
            title='Test Job',
            description='Test Description'
        )
        
        # Create test application
        self.application = JobApplication.objects.create(
            applicant=self.user,
            job=self.job
        )
        
        # Create test interview
        self.interview = Interview.objects.create(
            application=self.application
        )
        
        # Create test question
        self.question = InterviewQuestion.objects.create(
            job=self.job,
            question='Test question?'
        )
        self.interview.questions.add(self.question)

    def test_gemini_analysis(self):
        # Create a test video file
        video_path = 'path/to/test/video.mp4'  # You'll need a test video file
        if os.path.exists(video_path):
            with open(video_path, 'rb') as f:
                video_content = f.read()
                
            video_file = SimpleUploadedFile(
                "test_video.mp4",
                video_content,
                content_type="video/mp4"
            )
            
            # Add video to interview
            self.interview.video_response = video_file
            self.interview.save()
            
            # Test analysis
            service = GeminiAnalysisService()
            try:
                result = service.analyze_interview(self.interview.id)
                self.assertIsNotNone(result)
                self.interview.refresh_from_db()
                self.assertEqual(self.interview.analysis_status, 'completed')
            except Exception as e:
                self.fail(f"Analysis failed with error: {str(e)}") 