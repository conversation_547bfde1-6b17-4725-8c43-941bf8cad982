from django.core.management.base import BaseCommand
from interviews.models import VideoResponse

class Command(BaseCommand):
    help = 'Analyze video responses using Gemini AI'

    def add_arguments(self, parser):
        parser.add_argument('response_id', type=int)

    def handle(self, *args, **options):
        try:
            response = VideoResponse.objects.get(id=options['response_id'])
            response.analyze_with_gemini()
            self.stdout.write(self.style.SUCCESS(f'Successfully analyzed video {options["response_id"]}'))
        except VideoResponse.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Video response {options["response_id"]} not found'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error analyzing video: {str(e)}')) 