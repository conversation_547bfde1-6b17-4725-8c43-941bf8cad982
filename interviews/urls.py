from django.urls import path
from . import views

app_name = 'interviews'  # Add namespace

urlpatterns = [
    path('start/<int:application_id>/', views.start_interview, name='start_interview'),
    path('save-response/<int:application_id>/', views.save_interview_response, name='save_interview_response'),
    path('review/<int:application_id>/', views.review_interview, name='review_interview'),
    path('feedback/<int:response_id>/', views.save_feedback, name='save_feedback'),
    path('status/<int:application_id>/', views.update_application_status, name='update_status'),
    path('submit/<int:interview_id>/', views.submit_interview, name='submit_interview'),
    path('analysis/<int:interview_id>/', views.view_analysis, name='view_analysis'),
    path('test/', views.test_analysis, name='test_analysis'),
    path('check-status/<int:interview_id>/', views.check_analysis_status, name='check_analysis_status'),
    path('analysis-data/<int:application_id>/', views.get_analysis_data, name='get_analysis_data'),
    path('record/<int:interview_id>/', views.record_response, name='record_response'),
    path('save-attempt/<int:interview_id>/', views.save_attempt, name='save_attempt'),
    path('attempts/<int:interview_id>/', views.view_attempts, name='view_attempts'),
] 