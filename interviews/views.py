from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import InterviewQuestion, VideoResponse, Interview, VideoAttempt
from jobs.models import JobApplication
from .forms import VideoResponseForm
from django.views.decorators.csrf import csrf_exempt
from django.core.files.base import ContentFile
import json
import logging
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from .services.gemini_service import GeminiAnalysisService
from django.utils.safestring import mark_safe
import markdown

logger = logging.getLogger(__name__)

@login_required
def start_interview(request, application_id):
    application = get_object_or_404(JobApplication, pk=application_id)
    
    # Verify this is the correct applicant
    if application.applicant != request.user:
        messages.error(request, "You can only access your own interviews.")
        return redirect('dashboard')
    
    # Check if interview already exists
    existing_interview = Interview.objects.filter(application=application).first()
    if existing_interview:
        messages.info(request, "You have already completed this interview.")
        return redirect('interviews:view_analysis', interview_id=existing_interview.id)
    
    # Get interview questions
    questions = application.job.interview_questions.all().order_by('order')
    
    if not questions.exists():
        messages.error(request, "No interview questions found for this job.")
        return redirect('jobs:job_detail', pk=application.job.pk)
    
    # Create new interview with the correct field name
    interview = Interview.objects.create(
        application=application,
        analysis_status='pending'  # Changed from 'status' to 'analysis_status'
    )
    
    return render(request, 'interviews/start_interview.html', {
        'application': application,
        'interview': interview,
        'questions': questions
    })

@login_required
def review_interview(request, application_id):
    if not request.user.is_employer():
        messages.error(request, "Only employers can review interviews.")
        return redirect('home')
    
    application = get_object_or_404(JobApplication, id=application_id, job__employer=request.user)
    responses = VideoResponse.objects.filter(application=application)
    
    return render(request, 'interviews/review.html', {
        'application': application,
        'responses': responses
    })

@login_required
def save_feedback(request, response_id):
    if not request.user.is_employer():
        return JsonResponse({'status': 'error', 'message': 'Unauthorized'}, status=403)
    
    response = get_object_or_404(VideoResponse, 
                                id=response_id, 
                                application__job__employer=request.user)
    
    if request.method == 'POST':
        response.feedback = request.POST.get('feedback', '')
        response.save()
        return JsonResponse({'status': 'success'})
    
    return JsonResponse({'status': 'error'}, status=400)

@login_required
def update_application_status(request, application_id):
    if not request.user.is_employer():
        messages.error(request, "Only employers can update application status.")
        return redirect('home')
    
    application = get_object_or_404(JobApplication, 
                                  id=application_id, 
                                  job__employer=request.user)
    
    if request.method == 'POST':
        status = request.POST.get('status')
        if status in dict(JobApplication.STATUS_CHOICES):
            application.status = status
            application.save()
            messages.success(request, "Application status updated successfully.")
        else:
            messages.error(request, "Invalid status.")
    
    return redirect('review_interview', application_id=application_id)

@csrf_exempt
@login_required
def save_interview_response(request, application_id):
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    try:
        application = get_object_or_404(JobApplication, id=application_id, applicant=request.user)
        
        if not request.FILES.get('video'):
            return JsonResponse({'error': 'No video file received'}, status=400)
        
        video_file = request.FILES['video']
        
        # Save the video response
        response = VideoResponse.objects.create(
            application=application,
            video_file=video_file,
            analysis_status='processing'
        )
        
        # Update application status
        application.video_interview_completed = True
        application.status = 'interview_completed'
        application.save()

        # Use new GeminiAnalysisService for analysis
        try:
            service = GeminiAnalysisService()
            analysis_result = service.analyze_interview(response.id)
            response.ai_analysis = {'status': 'success', 'analysis': analysis_result}
            response.analysis_status = 'completed'
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            response.analysis_status = 'failed'
            response.ai_analysis = {'status': 'error', 'message': str(e)}
        
        response.save()
        return JsonResponse({'status': 'success'})
        
    except Exception as e:
        logger.error(f"Video upload failed for application {application_id}: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def submit_interview(request, interview_id):
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    interview = get_object_or_404(Interview, pk=interview_id)
    if interview.application.applicant != request.user:
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    selected_attempt = interview.video_attempts.filter(is_selected=True).first()
    if not selected_attempt:
        return JsonResponse({'error': 'No recording selected'}, status=400)
    
    try:
        # Update interview status and trigger analysis
        interview.analysis_status = 'processing'
        interview.save()

        # Update application status
        application = interview.application
        application.status = 'interview_completed'
        application.save()
        
        # Trigger the analysis process
        analysis_service = GeminiAnalysisService()
        # Remove .delay() since we're not using Celery
        analysis_service.analyze_interview(interview.id)
        
        # Return a redirect URL to dashboard after successful submission
        return JsonResponse({'success': True, 'redirect_url': '/dashboard/'})
    except Exception as e:
        logger.error(f"Error submitting interview: {e}")
        # Update status to failed if analysis fails
        interview.analysis_status = 'failed'
        interview.analysis_error = str(e)
        interview.save()
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def view_analysis(request, interview_id):
    interview = get_object_or_404(Interview, pk=interview_id)
    
    # Check permissions
    if not (request.user == interview.application.applicant or 
            request.user == interview.application.job.employer):
        messages.error(request, "You don't have permission to view this analysis.")
        return redirect('dashboard')
    
    # Get questions
    questions = interview.get_questions()
    
    # Convert markdown to HTML if analysis is completed
    if interview.analysis_status == 'completed' and interview.ai_analysis:
        interview.ai_analysis = mark_safe(markdown.markdown(interview.ai_analysis))
    
    # Get selected video attempt
    selected_video = interview.video_attempts.filter(is_selected=True).first()
    
    context = {
        'interview': interview,
        'questions': questions,
        'application': interview.application,
        'job': interview.application.job,
        'selected_video': selected_video  # Add selected video to context
    }
    
    return render(request, 'interviews/analysis.html', context)

@login_required
def test_analysis(request):
    # Create a test interview if it doesn't exist
    test_interview, created = Interview.objects.get_or_create(
        application=JobApplication.objects.first(),  # Get any application for testing
        defaults={
            'analysis_status': 'pending'
        }
    )
    
    return render(request, 'interviews/test_upload.html', {
        'test_interview': test_interview
    })

@login_required
def check_analysis_status(request, interview_id):
    interview = get_object_or_404(Interview, id=interview_id)
    return JsonResponse({
        'status': interview.analysis_status,
        'analysis': interview.ai_analysis if interview.analysis_status == 'completed' else None,
        'error': interview.analysis_error if interview.analysis_status == 'failed' else None
    })

@login_required
def get_analysis_data(request, application_id):
    application = get_object_or_404(JobApplication, id=application_id)
    interview = application.interview_set.first()
    
    if not interview:
        return JsonResponse({
            'status': 'error',
            'error': 'No interview found'
        })
    
    # Get selected video
    selected_video = interview.video_attempts.filter(is_selected=True).first()
    
    # Convert analysis text to markdown if it exists
    analysis_text = interview.ai_analysis
    if analysis_text and interview.analysis_status == 'completed':
        # Ensure the text is properly formatted as markdown
        analysis_text = analysis_text.replace('\n', '  \n')  # Fix line breaks
    
    return JsonResponse({
        'status': interview.analysis_status,
        'analysis': analysis_text,
        'error': interview.analysis_error,
        'video_url': selected_video.video_file.url if selected_video else None
    })

@login_required
def record_response(request, interview_id):
    interview = get_object_or_404(Interview, pk=interview_id)
    
    if interview.application.applicant != request.user:
        messages.error(request, "You can only access your own interviews.")
        return redirect('dashboard')
    
    # Check attempt count
    attempt_count = interview.video_attempts.count()
    if attempt_count >= Interview.MAX_ATTEMPTS:
        messages.warning(request, "You've reached the maximum number of attempts.")
        return redirect('interviews:view_attempts', interview_id=interview.id)
    
    questions = interview.get_questions()
    if not questions.exists():
        messages.error(request, "No interview questions found.")
        return redirect('jobs:job_detail', pk=interview.application.job.pk)
    
    return render(request, 'interviews/interview.html', {
        'interview': interview,
        'questions': questions,
        'attempt_number': attempt_count + 1,
        'max_attempts': Interview.MAX_ATTEMPTS,
        'attempts_left': Interview.MAX_ATTEMPTS - attempt_count,
        'job': interview.application.job
    })

@login_required
def save_attempt(request, interview_id):
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    interview = get_object_or_404(Interview, pk=interview_id)
    if interview.application.applicant != request.user:
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    if interview.video_attempts.count() >= Interview.MAX_ATTEMPTS:
        return JsonResponse({'error': 'Maximum attempts reached'}, status=400)
    
    try:
        video_file = request.FILES.get('video')
        notes = request.POST.get('notes', '')
        
        attempt = VideoAttempt.objects.create(
            interview=interview,
            video_file=video_file,
            notes=notes,
            is_selected=not interview.video_attempts.exists()  # Select if first attempt
        )
        
        return JsonResponse({
            'success': True,
            'attempt_id': attempt.id,
            'attempts_left': Interview.MAX_ATTEMPTS - interview.video_attempts.count()
        })
    except Exception as e:
        logger.error(f"Error saving video attempt: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def view_attempts(request, interview_id):
    interview = get_object_or_404(Interview, pk=interview_id)
    if interview.application.applicant != request.user:
        messages.error(request, "Access denied.")
        return redirect('dashboard')
    
    if request.method == 'POST':
        attempt_id = request.POST.get('selected_attempt')
        if attempt_id:
            attempt = get_object_or_404(VideoAttempt, id=attempt_id, interview=interview)
            attempt.is_selected = True
            attempt.save()
            messages.success(request, "Selected attempt updated successfully.")
    
    attempts = interview.video_attempts.all()
    has_selected = attempts.filter(is_selected=True).exists()
    
    return render(request, 'interviews/view_attempts.html', {
        'interview': interview,
        'attempts': attempts,
        'can_record_more': interview.video_attempts.count() < Interview.MAX_ATTEMPTS,
        'has_selected': has_selected
    }) 