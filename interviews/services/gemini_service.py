import os
import google.generativeai as genai
from time import sleep
from django.conf import settings
from interviews.models import Interview, InterviewQuestion, AIPrompt
import logging
from django.db import transaction
from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives

logger = logging.getLogger(__name__)

class GeminiAnalysisService:
    def __init__(self):
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.default_model = 'gemini-2.0-flash-exp'
        self.max_retries = 10
        self.retry_delay = 15

    def _get_model(self):
        """Get the model based on active prompt settings or use default"""
        active_prompt = AIPrompt.get_active_prompt()
        model_name = active_prompt.model_name if active_prompt else self.default_model
        return genai.GenerativeModel(model_name)

    def _get_prompt(self, questions_text):
        """Get analysis prompt from active prompt or use default"""
        active_prompt = AIPrompt.get_active_prompt()
        print(f"Active prompt: {active_prompt}")
        if active_prompt:
            try:
                return active_prompt.prompt_text.format(questions=questions_text)
            except Exception as e:
                logger.error(f"Error formatting custom prompt: {e}")
                
        # Fallback to default prompt
        return self._generate_default_prompt(questions_text)

    def _delete_uploaded_file(self, file_name):
        """Delete a specific uploaded file from Gemini"""
        try:
            file = genai.get_file(file_name)
            file.delete()
            logger.info(f"Successfully deleted file: {file_name}")
        except Exception as e:
            logger.error(f"Error deleting file {file_name}: {e}")

    def analyze_interview(self, interview_id):
        from interviews.models import Interview
        
        uploaded_file_name = None
        try:
            interview = Interview.objects.get(id=interview_id)
            selected_attempt = interview.video_attempts.filter(is_selected=True).first()
            
            if not selected_attempt:
                raise ValueError("No selected video attempt found")

            # Use the selected attempt's video file for analysis
            video_file = selected_attempt.video_file
            
            logger.info(f"Starting analysis for interview {interview_id}")
            
            # Upload to Gemini
            try:
                uploaded_file = genai.upload_file(video_file.path, mime_type="video/webm")
                uploaded_file_name = uploaded_file.name
                logger.info(f"File uploaded successfully: {uploaded_file_name}")
            except Exception as e:
                logger.error(f"File upload failed: {str(e)}")
                raise

            # Wait for processing with increased patience
            counter = 0
            while counter < self.max_retries:
                try:
                    logger.info(f"Checking file status, attempt {counter + 1}")
                    file_status = genai.get_file(uploaded_file_name)
                    logger.info(f"File status: {file_status.state}")
                    
                    if str(file_status.state) == 'State.ACTIVE':
                        logger.info("File processing completed successfully")
                        break
                    elif str(file_status.state) == 'State.FAILED':
                        raise Exception("File processing failed on Gemini side")
                        
                except Exception as e:
                    logger.error(f"Error checking file status: {str(e)}")
                    if counter == self.max_retries - 1:
                        raise
                
                counter += 1
                sleep(self.retry_delay)

            if counter == self.max_retries:
                raise TimeoutError("Video processing timed out after maximum retries")

            # Get the appropriate model and generate content
            try:
                model = self._get_model()
                
                # Get questions and format prompt
                questions = interview.get_questions()
                questions_text = "\n".join([
                    f"{idx+1}. {q.question}" 
                    for idx, q in enumerate(questions)
                ])
                
                prompt = self._get_prompt(questions_text)
                
                # Generate content
                response = model.generate_content([
                    uploaded_file,
                    prompt
                ])
                
                if response and response.text:
                    with transaction.atomic():
                        # Extract score from the analysis
                        score = None
                        analysis_lines = response.text.split('\n')
                        for i, line in enumerate(analysis_lines):
                            if 'Final Rating' in line:
                                try:
                                    # First check if score is on the same line
                                    if ':' in line:
                                        score_text = line.split(':')[1].strip()
                                        score_text = score_text.replace('**', '').strip()
                                        if '/' in score_text:
                                            score = float(score_text.split('/')[0].strip())
                                            if 0 <= score <= 10:
                                                logger.info(f"Successfully extracted score from same line: {score}")
                                                break

                                    # If not found in same line, look in next few lines
                                    if score is None:
                                        for j in range(1, 4):  # Check up to 3 lines after "Final Rating"
                                            if i + j >= len(analysis_lines):
                                                break
                                            
                                            score_text = analysis_lines[i + j].strip()
                                            if not score_text:  # Skip empty lines
                                                continue
                                            
                                            # Clean up the score text
                                            score_text = score_text.replace('**', '').strip()
                                            
                                            # Check if this line contains a score
                                            if '/' in score_text:
                                                score = float(score_text.split('/')[0].strip())
                                                if 0 <= score <= 10:
                                                    logger.info(f"Successfully extracted score from line {i+j}: {score}")
                                                    break
                                                else:
                                                    logger.warning(f"Score {score} out of valid range (0-10)")
                                                    score = None
                                        
                                        if score is not None:  # If we found a valid score, break the outer loop
                                            break
                                    
                                except (IndexError, ValueError) as e:
                                    logger.error(f"Error extracting score from '{score_text}': {e}")
                                    logger.error(f"Original line: {line}")
                                    score = None

                        # Save analysis and score
                        interview.ai_analysis = response.text
                        interview.ai_score = score
                        interview.analysis_status = 'completed'
                        
                        interview.save()
                        
                        # Update application status
                        application = interview.application
                        application.status = 'interview_completed'
                        application.save()
                        
                        # Send email only after successful analysis
                        try:
                            # Send notification to job seeker (keep existing)
                            # Extract Areas for Improvement from analysis
                            if interview.ai_analysis:
                                analysis_lines = interview.ai_analysis.split('\n')
                                in_improvements_section = False
                                improvement_lines = []

                                for line in analysis_lines:
                                    line = line.strip()
                                    if "Areas for Improvement:" in line:
                                        in_improvements_section = True
                                        continue
                                    elif "Final Rating" in line:
                                        in_improvements_section = False
                                        break
                                    elif in_improvements_section and line:
                                        improvement_lines.append(line)
                                
                                clean_improvements_html = []
                                clean_improvements_text = []
                                current_point_html = ""
                                current_point_text = ""

                                for line in improvement_lines:
                                    # Create HTML version (convert markdown to HTML)
                                    html_line = line
                                    # Convert bold (**text**) to HTML bold
                                    while "**" in html_line:
                                        html_line = html_line.replace("**", "<strong>", 1)
                                        html_line = html_line.replace("**", "</strong>", 1)

                                    # Convert italics (*text*) to HTML italics
                                    while "*" in html_line:
                                        html_line = html_line.replace("*", "<em>", 1)
                                        html_line = html_line.replace("*", "</em>", 1)

                                    # Handle bullet points
                                    if html_line.startswith('- '):
                                        html_line = "• " + html_line[2:]

                                    # Create plain text version (remove markdown)
                                    text_line = line.replace('**', '').replace('*', '')
                                    if text_line.startswith('- '):
                                        text_line = "• " + text_line[2:]

                                    # Check if this line starts a new improvement point
                                    if ":" in line and line.split(":", 1)[0].strip() and len(line.split(":", 1)[0].strip()) < 30:
                                        # If we were building a previous point, add it to our lists
                                        if current_point_html:
                                            clean_improvements_html.append(current_point_html)
                                        if current_point_text:
                                            clean_improvements_text.append(current_point_text)
                                        current_point_html = html_line
                                        current_point_text = text_line
                                    else:
                                        # This is a continuation of the current point
                                        current_point_html += " " + html_line
                                        current_point_text += " " + text_line

                                # Add the last point if there is one
                                if current_point_html:
                                    clean_improvements_html.append(current_point_html)
                                if current_point_text:
                                    clean_improvements_text.append(current_point_text)

                                # Create formatted strings with proper spacing
                                improvements_html = "\n<br><br>\n".join(clean_improvements_html) if clean_improvements_html else "Our team will provide detailed feedback after review."
                                improvements_text = "\n\n".join(clean_improvements_text) if clean_improvements_text else "Our team will provide detailed feedback after review."

                                # Send email with improvements
                                context = {
                                    'applicant_name': application.applicant.get_full_name(),
                                    'job_title': application.job.title,
                                    'company_name': application.job.employer.company_name,
                                    'improvements': improvements_html
                                }
                                
                                # Send email to applicant with feedback
                                html_message = render_to_string('emails/interview_completed.html', context)
                                plain_message = render_to_string('emails/interview_completed.txt', context)
                                
                                email = EmailMultiAlternatives(
                                    subject=f'Interview Analysis Complete - {application.job.title}',
                                    body=plain_message,
                                    from_email=settings.DEFAULT_FROM_EMAIL,
                                    to=[application.applicant.email]
                                )
                                email.attach_alternative(html_message, "text/html")
                                email.send(fail_silently=False)
                            
                            # Send notification to employer
                            self._notify_employer(interview)
                            logger.info(f"Analysis email sent for interview {interview_id}")
                        except Exception as e:
                            logger.error(f"Failed to send analysis email: {str(e)}")
                    
                    return response.text
                else:
                    raise ValueError("No response generated from Gemini")

            finally:
                # Always try to delete the uploaded file, even if analysis fails
                if uploaded_file_name:
                    self._delete_uploaded_file(uploaded_file_name)

        except Exception as e:
            logger.error(f"Analysis failed for interview {interview_id}: {str(e)}")
            interview.analysis_status = 'failed'
            interview.analysis_error = str(e)
            interview.save()
            raise

    def delete_all_uploaded_files(self):
        """Deletes all uploaded files in Gemini."""
        try:
            files = genai.list_files()
            if not files:
                logger.info("No files to delete.")
                return

            for file in files:
                try:
                    file.delete()
                    logger.info(f"Deleted file: {file.name}")
                except Exception as delete_error:
                    logger.error(f"Error deleting file {file.name}: {delete_error}")

            logger.info("All files deleted (or attempted).")

        except Exception as e:
            logger.error(f"Error listing files for deletion: {e}")

    def _build_analysis_prompt(self, questions):
        prompt = """
        Please analyze this video interview. For each question, provide:
        1. A detailed evaluation of the response
        2. Key strengths identified
        3. Areas for improvement
        4. Overall impression
        
        Format your response in markdown with clear sections.
        
        Questions asked:
        """
        
        for question in questions:
            prompt += f"\nQ: {question.question}\n"
        
        prompt += """
        Additional evaluation criteria:
        - Communication skills (clarity, confidence, body language)
        - Technical knowledge and understanding
        - Problem-solving approach
        - Cultural fit and soft skills
        
        Please provide a comprehensive analysis with specific examples from the interview.
        """
        
        return prompt

    def _generate_default_prompt(self, questions_text):
        """Default prompt template"""
        return f"""Please watch the attached videos where the candidate answers the following questions:

{questions_text}

For each question, provide:
A brief summary of the candidate's approach.
Strengths and weaknesses in persuasiveness (e.g., rebuttals, use of data, relatability).
Strengths and weaknesses in communication (e.g., clarity, jargon use, tone, body language if visible).

Evaluation Criteria:

**Persuasiveness**:
How convincing and compelling is the candidate when addressing these questions?
Consider their ability to engage, reassure, and build rapport.

**Sales Characteristics**:
Evaluate objection handling, rapport-building, persuasion tactics, trustworthiness, and enthusiasm.

**Communication Skills**:
Assess verbal clarity, tone, pacing, and body language (if applicable).
Consider the effectiveness of nonverbal cues and overall audience engagement.

**Overall Effectiveness**:
How clear, articulate, and responsive is the candidate in delivering their message?
Evaluate the logical flow, depth of content, and use of examples or data to support claims.

**Confidence**
Assess the candidate's level of confidence throughout their responses.
Does the candidate appear self-assured, knowledgeable, and trustworthy?

**Additional Sales Qualities**
Evaluate other essential sales attributes, such as empathy, adaptability, product knowledge, and rapport building.""" 

    def _notify_employer(self, interview):
        """Send notification to employer about completed interview analysis"""
        context = {
            'employer_name': interview.application.job.employer.company_name,
            'job_title': interview.application.job.title,
            'candidate_name': interview.application.applicant.get_full_name() or interview.application.applicant.username,
            'ai_score': interview.ai_score,
            'interview_id': interview.id,
            'site_url': settings.SITE_URL,
            'site_name': 'Job Portal'
        }
        
        # Render email templates
        html_message = render_to_string('emails/employer_interview_completed.html', context)
        plain_message = render_to_string('emails/employer_interview_completed.txt', context)
        
        # Send email
        email = EmailMultiAlternatives(
            subject=f'Interview Analysis Complete - {interview.application.job.title}',
            body=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[interview.application.job.employer.email]
        )
        email.attach_alternative(html_message, "text/html")
        email.send(fail_silently=False)
        
        logger.info(f"Employer notification sent for interview {interview.id}") 