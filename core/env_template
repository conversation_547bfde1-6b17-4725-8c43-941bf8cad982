# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-b)hy-_7t5-o%-zrq(rsn04l4x=)xa&l64xwwe)$_w03&6hiu1z
ALLOWED_HOSTS=neu-hire.com,*
# Database Configuration
DB_NAME=video_analysis_local_testing
DB_USER=postgres
DB_PASSWORD=123456
DB_HOST=***********
DB_PORT=5432



# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=seix qcsu mbwa iqgf
DEFAULT_FROM_EMAIL=Job Portal Team <<EMAIL>>



# Site Configuration
SITE_URL=neu-hire.com



# File Upload Settings
MAX_UPLOAD_SIZE=10485760



# Google AI Studio Settings
GEMINI_API_KEY=AIzaSyBUsCTn2CPpnQKnncrcZrsqyqZloMXvrJc



# Password Reset Settings
PASSWORD_RESET_TIMEOUT=86400