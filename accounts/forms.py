from django import forms
from django.contrib.auth.forms import User<PERSON><PERSON>tionForm, UserChangeForm, PasswordResetForm
from .models import CustomUser
from django.core.validators import FileExtensionValidator
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags

class CustomUserCreationForm(UserCreationForm):
    user_type = forms.ChoiceField(
        choices=CustomUser.USER_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'custom-radio'})
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={'placeholder': ' '})
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={'placeholder': ' '})
    )
    
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'placeholder': ' '})
    )
    
    phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={'placeholder': ' '})
    )
    
    experience_years = forms.IntegerField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={'placeholder': ' '})
    )
    
    skills = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'rows': 3,
            'placeholder': ' '
        })
    )
    
    company_name = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'Enter your company name',
            'class': 'form-control'
        })
    )
    
    company_website = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={
            'class': 'form-control',
            'placeholder': 'https://www.example.com'
        })
    )
    
    profile_image = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*',
            'data-preview': 'profilePreview'  # For preview functionality
        }),
        help_text='Upload a profile photo (optional)'
    )
    
    resume = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.pdf,.doc,.docx',
        }),
        help_text='Upload your resume (PDF, DOC, DOCX)'
    )

    company_logo = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        })
    )

    class Meta:
        model = CustomUser
        fields = [
            'user_type', 
            'username', 
            'email', 
            'first_name', 
            'last_name',
            'profile_image',
            'password1', 
            'password2',
            'company_name',
            'company_website',
            'company_description',
            'company_logo'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add placeholders and classes for password fields
        self.fields['password1'].widget.attrs.update({'placeholder': ' '})
        self.fields['password2'].widget.attrs.update({'placeholder': ' '})
        self.fields['username'].widget.attrs.update({'placeholder': ' '})

    def clean(self):
        cleaned_data = super().clean()
        user_type = cleaned_data.get('user_type')
        
        if user_type == 'jobseeker':
            if not cleaned_data.get('first_name'):
                self.add_error('first_name', 'First name is required for job seekers')
            if not cleaned_data.get('last_name'):
                self.add_error('last_name', 'Last name is required for job seekers')
        
        elif user_type == 'employer':
            if not cleaned_data.get('company_name'):
                self.add_error('company_name', 'Company name is required for employers')
        
        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.user_type = self.cleaned_data['user_type']
        user.first_name = self.cleaned_data.get('first_name', '')
        user.last_name = self.cleaned_data.get('last_name', '')
        
        # Handle file uploads
        if self.cleaned_data.get('profile_image'):
            user.profile_image = self.cleaned_data['profile_image']
        if self.cleaned_data.get('resume'):
            user.resume = self.cleaned_data['resume']
        
        if commit:
            user.save()
            
            # Save additional fields based on user type
            if self.cleaned_data['user_type'] == 'jobseeker':
                user.phone_number = self.cleaned_data.get('phone_number', '')
                user.experience_years = self.cleaned_data.get('experience_years')
                user.skills = self.cleaned_data.get('skills', '')
            else:
                user.company_name = self.cleaned_data.get('company_name', '')
                if self.cleaned_data.get('company_logo'):
                    user.company_logo = self.cleaned_data['company_logo']
            
            user.save()
        
        return user

    def clean_profile_image(self):
        image = self.cleaned_data.get('profile_image')
        if image and hasattr(image, 'content_type'):  # Check if it's a new file upload
            if image.size > 5 * 1024 * 1024:  # 5MB limit
                raise forms.ValidationError("Image file too large ( > 5MB )")
            if not image.content_type.startswith('image/'):
                raise forms.ValidationError("File type not supported")
        return image

    def clean_resume(self):
        resume = self.cleaned_data.get('resume')
        if resume:
            if resume.size > 10 * 1024 * 1024:  # 10MB limit
                raise forms.ValidationError("Resume file too large ( > 10MB )")
            allowed_types = ['application/pdf', 'application/msword', 
                           'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
            if resume.content_type not in allowed_types:
                raise forms.ValidationError("File type not supported. Please upload PDF or DOC/DOCX")
        return resume

class CustomUserChangeForm(UserChangeForm):
    class Meta:
        model = CustomUser
        fields = ('email', 'username')

class JobSeekerProfileForm(forms.ModelForm):
    profile_image = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        })
    )
    
    class Meta:
        model = CustomUser
        fields = [
            'first_name', 
            'last_name', 
            'email', 
            'phone_number',
            'profile_image',
            'skills',
            'experience_years',
            'resume'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'First Name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Last Name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Email Address'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Phone Number'
            }),
            'experience_years': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Years of Experience'
            }),
            'skills': forms.Textarea(attrs={
                'rows': 3,
                'class': 'form-control',
                'placeholder': 'Enter your skills (comma separated)'
            }),
            'resume': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            })
        }

    def clean_first_name(self):
        first_name = self.cleaned_data.get('first_name')
        if not first_name:
            raise forms.ValidationError("First name is required")
        return first_name

    def clean_last_name(self):
        last_name = self.cleaned_data.get('last_name')
        if not last_name:
            raise forms.ValidationError("Last name is required")
        return last_name

    def clean_profile_image(self):
        image = self.cleaned_data.get('profile_image')
        if image and hasattr(image, 'content_type'):  # Check if it's a new file upload
            if image.size > 5 * 1024 * 1024:  # 5MB limit
                raise forms.ValidationError("Image file too large ( > 5MB )")
            if not image.content_type.startswith('image/'):
                raise forms.ValidationError("File type not supported")
        return image

class EmployerProfileForm(forms.ModelForm):
    company_logo = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        })
    )
    
    class Meta:
        model = CustomUser
        fields = [
            'company_name',
            'company_description',
            'company_website',
            'company_location',
            'company_logo',
            'email',
            'phone_number'
        ]
        widgets = {
            'company_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Company Name'
            }),
            'company_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Tell us about your company'
            }),
            'company_website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://'
            }),
            'company_location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Company Location'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Email Address'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Phone Number'
            })
        }

    def clean_company_name(self):
        company_name = self.cleaned_data.get('company_name')
        if not company_name:
            raise forms.ValidationError("Company name is required")
        return company_name

    def clean_company_logo(self):
        logo = self.cleaned_data.get('company_logo')
        if logo and hasattr(logo, 'content_type'):  # Check if it's a new file upload
            if logo.size > 5 * 1024 * 1024:  # 5MB limit
                raise forms.ValidationError("Logo file too large ( > 5MB )")
            if not logo.content_type.startswith('image/'):
                raise forms.ValidationError("File type not supported")
        return logo

class CustomPasswordResetForm(PasswordResetForm):
    def send_mail(self, subject_template_name, email_template_name,
                 context, from_email, to_email, html_email_template_name=None):
        """
        Send a django.core.mail.EmailMultiAlternatives to `to_email`.
        """
        subject = render_to_string(subject_template_name, context)
        subject = ''.join(subject.splitlines())
        
        # Render HTML version
        html_message = render_to_string('registration/password_reset_email.html', context)
        # Create plain text version
        plain_message = strip_tags(html_message)

        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=from_email,
            to=[to_email]
        )
        
        email.attach_alternative(html_message, "text/html")
        email.send() 