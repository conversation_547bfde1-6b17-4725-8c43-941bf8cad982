from django.contrib.auth.models import AbstractUser
from django.db import models
import uuid
from django.core.mail import EmailMultiAlternatives
from django.utils.html import strip_tags

class CustomUser(AbstractUser):
    USER_TYPE_CHOICES = (
        ('jobseeker', 'Job Seeker'),
        ('employer', 'Employer'),
    )
    
    user_type = models.CharField(max_length=10, choices=USER_TYPE_CHOICES)
    phone_number = models.CharField(max_length=15, blank=True, default='')
    email_verified = models.BooleanField(default=False)
    email_verification_token = models.UUIDField(default=uuid.uuid4, editable=False)
    
    # Job Seeker Fields
    experience_years = models.IntegerField(null=True, blank=True)
    skills = models.TextField(blank=True, default='')
    resume = models.FileField(upload_to='resumes/', null=True, blank=True)
    
    # Employer Fields
    company_name = models.Char<PERSON>ield(max_length=100, blank=True, default='')
    company_description = models.TextField(blank=True, default='')
    company_website = models.URLField(max_length=200, blank=True, default='')
    company_location = models.CharField(max_length=200, blank=True, default='')
    company_logo = models.ImageField(upload_to='company_logos/', null=True, blank=True)
    
    # New field
    profile_image = models.ImageField(
        upload_to='profile_images/',
        null=True,
        blank=True,
        verbose_name='Profile Photo'
    )

    # Make sure these fields exist and are properly filled
    first_name = models.CharField(max_length=30, blank=True)
    last_name = models.CharField(max_length=30, blank=True)

    def get_full_name(self):
        full_name = f"{self.first_name} {self.last_name}".strip()
        return full_name if full_name else self.username

    def is_employer(self):
        return self.user_type == 'employer'
    
    def is_jobseeker(self):
        return self.user_type == 'jobseeker'

    def save(self, *args, **kwargs):
        # Ensure default values are set before saving
        if not self.phone_number:
            self.phone_number = ''
        if not self.skills:
            self.skills = ''
        if not self.company_name:
            self.company_name = ''
        if not self.company_description:
            self.company_description = ''
        if not self.company_website:
            self.company_website = ''
        if not self.company_location:
            self.company_location = ''
            
        super().save(*args, **kwargs)

    def send_verification_email(self):
        from django.template.loader import render_to_string
        from django.conf import settings
        from django.urls import reverse
        
        verification_url = f"{settings.SITE_URL}{reverse('verify_email', kwargs={'token': self.email_verification_token})}"
        
        context = {
            'user': self,
            'verification_url': verification_url,
            'site_name': 'Job Portal'
        }
        
        subject = 'Verify your email address'
        # Render HTML version
        html_message = render_to_string('emails/email_verification_email.html', context)
        # Create plain text version
        plain_message = strip_tags(html_message)
        
        # Create email
        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[self.email]
        )
        
        # Attach HTML version
        email.attach_alternative(html_message, "text/html")
        email.send()

    class Meta:
        db_table = 'accounts_customuser'
        verbose_name = 'User'
        verbose_name_plural = 'Users' 