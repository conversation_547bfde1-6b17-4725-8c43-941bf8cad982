from django.shortcuts import render, redirect
from django.contrib.auth import login
from django.contrib.auth.decorators import login_required
from django.views.generic import CreateView
from django.urls import reverse_lazy
from .forms import (
    CustomUserCreationForm, 
    JobSeekerProfileForm,
    EmployerProfileForm
)
from django.db.models import Q
from jobs.models import JobListing, JobApplication
from django.contrib import messages
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import authenticate
from django.contrib.auth.views import LoginView
from .models import CustomUser  # Import your custom user model
from interviews.models import Interview
from django.views.decorators.cache import never_cache
from django.views.decorators.vary import vary_on_headers
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

@never_cache
@vary_on_headers('Cookie')
def home(request):
    """Home view with proper cache control to prevent authentication state issues."""
    context = {
        'user': request.user,
        'is_authenticated': request.user.is_authenticated,
    }
    
    response = render(request, 'home.html', context)
    
    # Set cache control headers to prevent caching issues
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    
    return response

def signup(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                user = form.save(commit=False)
                user.is_active = False  # User can't login until email is verified
                user.save()
                
                # Send verification email
                user.send_verification_email()
                
                messages.success(request, 
                    'Your account has been created! Please check your email to verify your account.')
                return redirect('login')
            except Exception as e:
                messages.error(request, f"Error creating account: {str(e)}")
                print(f"Error in signup: {str(e)}")  # For debugging
        else:
            print("Form errors:", form.errors)  # For debugging
            messages.error(request, "Please correct the errors below.")
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'registration/signup.html', {'form': form})

@login_required
def dashboard(request):
    if not request.user.is_authenticated:
        return redirect('login')
        
    if request.user.is_jobseeker():
        filter_type = request.GET.get('filter')
        applications_qs = JobApplication.objects.filter(applicant=request.user).select_related('job', 'job__employer').order_by('-applied_at')
        
        if filter_type == 'under_review':
            applications = applications_qs.filter(status='reviewing')
        elif filter_type == 'pending_interview':
            # Find applications with at least one pending interview
            applications = applications_qs.filter(interview__analysis_status='pending').distinct()
        else:
            applications = applications_qs

        total_applications = applications_qs.count()
        pending_interviews = Interview.objects.filter(application__applicant=request.user, analysis_status='pending').count()
        # print(pending_interviews)
        applications_under_review = applications_qs.filter(status='reviewing').count()
        paginator = Paginator(applications, 5)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        context = {
            'recent_applications': applications[:5],  # Show filtered or all
            'all_applications': applications,         # For full list if needed
            'total_applications': total_applications,
            'pending_interviews': pending_interviews,
            'applications_under_review': applications_under_review,
            'filter_type': filter_type,
            'page_obj': page_obj,
        }
        return render(request, 'accounts/jobseeker_dashboard.html', context)
    elif request.user.is_employer():
        # Get recent applications with interview data
        recent_applications = JobApplication.objects.filter(
            job__employer=request.user
        ).select_related(
            'applicant',
            'job'
        ).prefetch_related(
            'interview_set'
        ).order_by('-applied_at')[:5]

        # Get job listings
        job_listings = JobListing.objects.filter(
            employer=request.user
        ).order_by('-created_at')
        paginator = Paginator(job_listings, 5)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'recent_applications': recent_applications,
            'job_listings': job_listings,
            'total_applications': JobApplication.objects.filter(job__employer=request.user).count(),
            'new_applications': JobApplication.objects.filter(
                job__employer=request.user,
                status='pending'
            ).count(),
            'page_obj': page_obj,
        }
        
        return render(request, 'accounts/employer_dashboard.html', context)
    else:
        messages.error(request, "Invalid user type. Please contact support.")
        return redirect('home')

@login_required
def profile_edit(request):
    # Determine user type and set appropriate form and template
    if request.user.is_employer():
        form_class = EmployerProfileForm
        template_name = 'accounts/employer_profile.html'
        success_url = 'dashboard'  # or 'employer_dashboard' if you have a specific one
    else:
        form_class = JobSeekerProfileForm
        template_name = 'accounts/jobseeker_profile.html'
        success_url = 'dashboard'

    if request.method == 'POST':
        form = form_class(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            try:
                form.save()
                messages.success(request, 'Profile updated successfully!')
                return redirect(success_url)
            except Exception as e:
                print("Error saving form:", str(e))
                messages.error(request, f'Error updating profile: {str(e)}')
        else:
            print("Form errors:", form.errors)
            messages.error(request, 'Please correct the errors below.')
    else:
        form = form_class(instance=request.user)
    
    return render(request, template_name, {
        'form': form,
        'user': request.user
    })

def verify_email(request, token):
    try:
        user = CustomUser.objects.get(email_verification_token=token)
        
        # Check if token is expired (24 hours)
        if timezone.now() - user.date_joined > timedelta(days=1):
            messages.error(request, 'Verification link has expired. Please sign up again.')
            user.delete()
            return redirect('signup')
            
        user.email_verified = True
        user.is_active = True
        user.save()
        
        messages.success(request, 'Your email has been verified successfully! You can now login.')
        return render(request, 'registration/email_verification_success.html')
        
    except CustomUser.DoesNotExist:
        messages.error(request, 'Invalid verification link')
        return redirect('signup')

# Update login view to check for email verification
class CustomLoginView(LoginView):
    def form_valid(self, form):
        user = form.get_user()
        if not user.email_verified:
            messages.error(self.request, 
                'Please verify your email address before logging in. Check your inbox for the verification link.')
            return self.form_invalid(form)
        return super().form_valid(form) 