from setuptools import setup, find_packages

setup(
    name="cyordereddict",
    version="0.2.2",
    description="Compatibility layer for cyordereddict using built-in collections.OrderedDict",
    author="Compatibility Package",
    author_email="<EMAIL>",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.6",
)

