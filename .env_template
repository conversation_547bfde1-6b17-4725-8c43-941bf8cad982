# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-b)hy-_7t5-o%-zrq(rsn04l4x=)xa&l64xwwe)$_w03&6hiu1z
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com,.ngrok-free.app,.ngrok.app

# Database Configuration
DB_NAME=<data_base_name>
DB_USER=<data_base_username>
DB_PASSWORD=<data_base_password>
DB_HOST=localhost
DB_PORT=5432

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<Email For sending notification>
EMAIL_HOST_PASSWORD=<Emial Application Password>
DEFAULT_FROM_EMAIL=Job Portal Team <Email>

# Site Configuration
SITE_URL=http://127.0.0.1:8000

# File Upload Settings
MAX_UPLOAD_SIZE=10485760

# Google AI Studio Settings
GEMINI_API_KEY=<GEMINI_API_KEY>

# Password Reset Settings
PASSWORD_RESET_TIMEOUT=86400 