# Python
*.py[cod]
*$py.class
__pycache__/
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
static/admin/
static/debug_toolbar/


# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-workspace
*.sublime-project

# Database
*.db
*.sqlite3
*.psql

# Environment variables
.env
.env.local
.env.*.local

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Media files
media/
uploads/

# Compiled static files
staticfiles/
static_root/

# Local development settings
local_settings.py

# Celery
celerybeat-schedule
celerybeat.pid

# Node
node_modules/
package-lock.json
yarn.lock

# Test database
test_db.sqlite3

# Backup files
*.bak
*.swp
*~

# Secret keys
*.pem
*.key 