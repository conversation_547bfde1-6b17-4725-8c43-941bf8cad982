@import url("https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&family=Unbounded&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@font-face {
  font-family: "ClashDisplay-Medium";
  src: url("../fonts/ClashDisplay-Medium.woff2") format("woff2"), url("../fonts/ClashDisplay-Medium.woff") format("woff"), url("../fonts/ClashDisplay-Medium.ttf") format("truetype");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: "ClashDisplay-Semibold";
  src: url("../fonts/ClashDisplay-Semibold.woff2") format("woff2"), url("../fonts/ClashDisplay-Semibold.woff") format("woff"), url("../fonts/ClashDisplay-Semibold.ttf") format("truetype");
  font-weight: 600;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: "ClashDisplay-Regular";
  src: url("../fonts/ClashDisplay-Regular.woff2") format("woff2"), url("../fonts/ClashDisplay-Regular.woff") format("woff"), url("../fonts/ClashDisplay-Regular.ttf") format("truetype");
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}
* {
  box-sizing: border-box;
}

body {
  background: #fff;
  font-size: 16px;
  line-height: 160%;
  font-family: "Poppins", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  color: #646464;
  margin: 0;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  color: #1565C0;
  text-decoration: underline;
}

a:hover {
  text-decoration: none;
}

a:focus {
  outline: none;
}

a:focus-visible {
  outline: 1px dotted;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
}

p {
  margin: 0 0 30px;
}

.site-container {
  max-width: 1240px;
  margin: 0 auto;
  padding: 0 20px;
}
.site-container--wide {
  max-width: 1350px;
}

.section-title {
  font-family: "ClashDisplay-Semibold";
  font-size: 64px;
  line-height: 110%;
  margin-bottom: 70px;
  color: #333;
}

.text-blue {
  color: #1565C0;
}

.btn {
  font-family: "Poppins", sans-serif;
  font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
  font-size: 11px;
  line-height: 190%;
  text-align: center;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 12px;
  border-radius: 13px;
  min-width: 108px;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.35s ease-in-out;
}
.btn--primary {
  color: #fff;
  background: #1565C0;
  border-color: #1565C0;
}
.btn--primary:hover, .btn--primary:focus {
  background: transparent;
  color: #1565C0;
}
.btn--primary-white {
  background: #fff;
  color: #1565C0;
  border-color: #fff;
}
.btn--primary-white:hover, .btn--primary-white:focus {
  color: #fff;
}
.btn--secondary {
  color: #000;
  border-color: #000;
}
.btn--secondary:hover, .btn--secondary:focus {
  color: #fff;
  background: #000;
}
.btn--secondary-white {
  border-color: #fff;
  color: #fff;
}
.btn--secondary-white:hover, .btn--secondary-white:focus {
  color: #1565C0;
  background: #fff;
}

.card {
  padding: 36px 40px;
  background: #fff;
  box-shadow: 0px 4.86px 29.18px 0px rgba(0, 0, 0, 0.08);
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.card--text-center {
  text-align: center;
  align-items: center;
}
.card__title {
  font-family: "ClashDisplay-Medium";
  font-size: 24px;
  line-height: 140%;
  font-weight: normal;
  color: #070828;
  margin-bottom: 12px;
  padding: 0 24px;
}
.card__description {
  color: #8E939C;
  font-size: 19px;
  line-height: 160%;
  font-weight: 300;
  margin: 0;
}

.cta-section {
  padding: 122px 0 70px;
}
.cta-section__inner {
  background-color: #1565C0;
  background-image: url(../images/bg-cta-section.png);
  background-repeat: no-repeat;
  background-position: top right;
  padding: 60px 75px;
  border-radius: 43px;
  color: #fff;
  position: relative;
}
.cta-section__title {
  font-family: "ClashDisplay-Semibold";
  font-weight: 400;
  font-size: 87px;
  line-height: 100%;
  margin-bottom: 0;
}
.cta-section__sub-title {
  font-size: 48px;
  line-height: 100%;
  font-family: "ClashDisplay-Regular";
  font-weight: 400;
  margin: 0 0 20px;
}
.cta-section__description {
  font-size: 20px;
  line-height: 160%;
  font-weight: 300;
  margin: 0 0 30px;
}
.cta-section__btn-holder {
  display: flex;
  gap: 10px;
}
.cta-section__img {
  position: absolute;
  bottom: -25px;
  right: 0;
}

.vouch {
  padding: 10px 0 113px;
  text-align: center;
  background: url(../images/bg-vouch.png) no-repeat 50% 170%;
}
.vouch__title {
  font-family: "ClashDisplay-Medium";
  font-weight: 600;
  font-size: 335px;
  line-height: 100%;
  margin: 0;
  color: #1565C0;
}
.vouch__sub-title {
  font-weight: 400;
  font-size: 48px;
  line-height: 100%;
  margin: 0;
  color: #333;
}

.site-header {
  padding: 46px 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 2;
}

.site-header .header-inner {
  background: #fff;
  box-shadow: 0px 0px 60px 52.5px rgba(0, 0, 0, 0.05);
  padding: 16px 32px;
  border-radius: 75px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-logo-holder {
  text-decoration: none;
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-logo-text {
  color: #1E1E1E;
  font-size: 20px;
  line-height: 100%;
  font-family: "Unbounded", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

.site-header .header-right {
  display: flex;
  align-items: center;
  gap: 56px;
}

.site-navigation ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 36px;
  font-family: "Poppins", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  line-height: 100%;
}

.site-navigation ul li a {
  color: #000;
  text-decoration: none;
  transition: color 0.35s ease-in-out;
}

.site-navigation ul li a:hover,
.site-navigation ul li a:focus {
  color: #1565C0;
}

.site-footer {
  background: #000;
  color: rgba(255, 255, 255, 0.5);
  height: 200px;
}
.site-footer__inner {
  display: flex;
  gap: 20px;
}
.site-footer__col {
  flex: 1;
  flex-basis: 20%;
}

.home-hero {
  background-color: #F5F5F5;
  min-height: 917px;
  padding-top: 213px;
  position: relative;
}

.home-hero .text-holder {
  position: relative;
  z-index: 1;
}

.home-hero .title {
  font-family: "ClashDisplay-Semibold";
  font-size: 72px;
  line-height: 110%;
  color: #25324B;
  padding-bottom: 54px;
  background-image: url(../images/lines.svg);
  background-repeat: no-repeat;
  background-position: 0 100%;
  margin-bottom: 16px;
  max-width: 568px;
}

.home-hero .title span {
  color: #1565C0;
}

.home-hero .description {
  font-size: 24px;
  line-height: 160%;
  max-width: 510px;
  margin-bottom: 36px;
}

.hero-search-form {
  display: flex;
  max-width: 852px;
  background: #fff;
  padding: 16px;
  border-radius: 30px;
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.0509803922);
  margin-bottom: 45px;
}

.hero-search-form .input-holder {
  flex-grow: 1;
  flex-basis: 0;
  display: flex;
  gap: 16px;
  padding: 0 16px;
}

.hero-search-form .input-holder .icon-holder {
  display: flex;
  align-items: center;
}

.hero-search-form input[type=text] {
  margin: 0;
  border: 0;
  border-radius: 0;
  box-shadow: none;
  font-family: "Poppins", sans-serif;
  color: #000;
  font-size: 16px;
  line-height: 160%;
  outline: none;
  border-bottom: 1px solid #D6DDEB;
  flex-basis: 0;
  flex-grow: 1;
}

.hero-search-form input[type=text]::placeholder {
  color: #7C8493;
  opacity: 0.5;
}

.hero-search-form input[type=text]::-webkit-input-placeholder {
  color: #7C8493;
  opacity: 0.5;
}

.hero-search-form input[type=text]:-moz-placeholder {
  color: #7C8493;
  opacity: 0.5;
}

.hero-search-form input[type=text]::-moz-placeholder {
  color: #7C8493;
  opacity: 0.5;
}

.hero-search-form input[type=text]:-ms-input-placeholder {
  color: #7C8493;
  opacity: 0.5;
}

.hero-search-form .select2-container {
  flex-basis: 0;
  flex-grow: 1;
  align-items: center;
  border-bottom: 1px solid #D6DDEB;
}

.hero-search-form .select2-container--default .select2-selection--single {
  border: 0;
  border-radius: 0;
  height: 100%;
  display: flex;
  align-items: center;
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding: 0;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  line-height: 160%;
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #7C8493;
  opacity: 0.5;
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__arrow {
  width: 16px;
  height: 16px;
  background: url(../images/angle-down.svg) no-repeat;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.35s ease-in-out;
}

.hero-search-form .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow {
  transform: translateY(-50%) rotate(180deg);
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}

.hero-search-form .btn-submit-holder {
  width: 213px;
}

.hero-search-form input[type=submit] {
  width: 100%;
  height: 57px;
  cursor: pointer;
  font-size: 15px;
}

.select2-dropdown {
  border: 0;
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.0509803922);
}

.home-hero .img-holder {
  position: absolute;
  right: 0;
  bottom: 0;
}

.home-hero .img-holder img {
  vertical-align: top;
}

.hero-jobs-available {
  position: absolute;
  right: 85px;
  top: 50%;
  backdrop-filter: blur(10px);
  box-shadow: 0px 4.76px 9.51px 0px rgba(51, 48, 48, 0.2509803922);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.6);
}

.hero-jobs-available img {
  display: block;
}

.hero-few-seconds {
  position: absolute;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  backdrop-filter: blur(10px);
  box-shadow: 0px 4.76px 9.51px 0px rgba(51, 48, 48, 0.2509803922);
  border-radius: 16px;
}

.hero-few-seconds img {
  display: block;
}

.how-it-works {
  padding: 60px 0;
  background: url(../images/bg-how-it-works.svg);
  background-repeat: no-repeat;
}
.how-it-works .tab-btn-holder {
  display: flex;
  justify-content: flex-end;
}
.how-it-works .tab-btn-wrapper {
  display: flex;
  gap: 10px;
  padding: 4px;
  border-radius: 9px;
  border: 1px solid #E1E1E1;
}
.how-it-works .tab-btn {
  padding: 10px 15px;
  background: transparent;
  border: 1px solid transparent;
  display: flex;
  gap: 10px;
  align-items: center;
  color: #9CA3AF;
  font-size: 16px;
  line-height: 23px;
  margin: 0;
  cursor: pointer;
  border-radius: 7px;
  transition: all 0.35s ease-in-out;
}
.how-it-works .tab-btn svg {
  display: block;
}
.how-it-works .tab-btn.active {
  background: #1565C0;
  color: #fff;
  border-color: #0A51AC;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.how-it-works .tab-btn.active svg path {
  fill: #fff;
}
.how-it-works .tab-content {
  padding: 80px 0 60px;
}
.how-it-works .tab-content__pane {
  gap: 20px;
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}
.how-it-works .tab-content__pane--active {
  display: flex;
}
.how-it-works .tab-content__pane--fade-in {
  opacity: 1;
}
.how-it-works .tab-content__right {
  flex-basis: 0;
  flex-grow: 1;
}
.how-it-works .tab-content__img-holder img {
  border-radius: 58px;
}
.how-it-works .tab-content__left {
  flex-basis: 0;
  flex-grow: 1;
}
.how-it-works .tab-content__title-holder {
  padding-left: 49px;
}
.how-it-works .tab-content__sub-title {
  font-family: "ClashDisplay-Medium";
  font-size: 24px;
  line-height: 140%;
  color: #1565C0;
  display: block;
  margin-bottom: 16px;
}
.how-it-works .tab-content__title {
  font-family: "ClashDisplay-Semibold";
  font-size: 56px;
  line-height: 120%;
  color: #333;
  margin-bottom: 42px;
}
.how-it-works .list {
  padding-left: 49px;
  border-left: 1px solid #1565C0;
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding-bottom: 70px;
}
.how-it-works .list__title {
  font-weight: 500;
  font-size: 20px;
  line-height: 140%;
  margin-bottom: 10px;
  color: #1565C0;
  display: flex;
  align-items: center;
  gap: 20px;
}
.how-it-works .list__title--text-large {
  font-size: 32px;
  line-height: 140%;
}
.how-it-works .list__description {
  margin: 0;
}

.why-vouch {
  padding: 80px 0 100px;
}
.why-vouch .section-title {
  text-align: center;
}
.why-vouch__inner-holder {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 0 25px;
}
.why-vouch .card {
  flex: 1;
}

.built-for-cta__inner-holder {
  border: 1px solid #1565C0;
  border-radius: 43px;
  padding: 85px 50px;
  background: linear-gradient(90.66deg, rgba(21, 101, 192, 0) 16.99%, rgba(21, 101, 192, 0.1) 95.81%), url(../images/bg-built-for.svg);
  background-repeat: no-repeat;
  background-position: top right;
}
.built-for-cta__text-holder {
  max-width: 754px;
}
.built-for-cta__sub-title {
  font-size: 32px;
  line-height: 140%;
  font-family: "ClashDisplay-Medium";
  display: block;
}
.built-for-cta__title {
  font-family: "ClashDisplay-Semibold";
  font-size: 56px;
  line-height: 110%;
  margin-bottom: 24px;
  color: #333;
}
.built-for-cta__description {
  color: #515B6F;
  font-weight: 300;
  font-size: 20px;
  line-height: 160%;
  max-width: 600px;
}

.testimonials {
  padding: 70px 0;
}
.testimonials__inner {
  display: flex;
  gap: 20px;
}
.testimonials__img-holder {
  width: 41.667%;
  padding: 0 20px;
}
.testimonials__holder {
  padding: 0 40px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 60px;
  flex-basis: 0;
  flex-grow: 1;
}
.testimonials__item {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.testimonials__description {
  color: #8E939C;
  font-weight: 300;
}
.testimonials__description p {
  margin: 0;
}
.testimonials__meta-info {
  display: flex;
  gap: 20px;
  align-items: center;
}
.testimonials__img {
  border-radius: 50%;
}
.testimonials__meta-text-holder {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.testimonials__by-name {
  font-weight: 400;
  font-size: 20px;
  line-height: 160%;
  margin: 0;
  color: #3A3A3A;
}
.testimonials__by-designation {
  font-size: 12px;
  line-height: 100%;
  color: #C4C5C8;
}

/*# sourceMappingURL=style.css.map */
