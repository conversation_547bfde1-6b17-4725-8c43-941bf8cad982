@use "../helpers/variables" as *;

.btn {
	font-family: $primary-font;
	font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
	font-size: 11px;
	line-height: 190%;
	text-align: center;
	text-decoration: none;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 12px 12px;
	border-radius: 13px;
	min-width: 108px;
	cursor: pointer;
	border: 1px solid transparent;
	transition: all 0.35s ease-in-out;

	&--primary {
		color: #fff;
		background: $primary-color;
		border-color: $primary-color;
		
		&:hover,
		&:focus {
			background: transparent;
			color: $primary-color;
		}

		&-white {
			background: #fff;
			color: $primary-color;
			border-color: #fff;

			&:hover,
			&:focus {
				color: #fff;
			}
		}
	}

	&--secondary {
		color: #000;
		border-color: #000;

		&:hover,
		&:focus {
			color: #fff;
			background: #000;
		}

		&-white {
			border-color: #fff;
			color: #fff;

			&:hover,
			&:focus {
				color: $primary-color;
				background: #fff;
			}
		}
	}
}