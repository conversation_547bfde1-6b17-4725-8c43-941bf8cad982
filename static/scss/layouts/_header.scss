@use "../helpers/variables" as *;

.site-header {
	padding: 46px 0;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 2;
}

.site-header .header-inner {
	background: #fff;
	box-shadow: 0px 0px 60px 52.5px rgba(0, 0, 0, 0.05);
	padding: 16px 32px;
	border-radius: 75px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.header-logo-holder {
	text-decoration: none;
	display: flex;
	gap: 12px;
	align-items: center;
}

.header-logo-text {
	color: #1E1E1E;
	font-size: 20px;
	line-height: 100%;
	font-family: "Unbounded", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

.site-header .header-right {
	display: flex;
	align-items: center;
	gap: 56px;
}

.site-navigation ul {
	margin: 0;
	padding: 0;
	list-style: none;
	display: flex;
	gap: 36px;
	font-family: $primary-font;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
	font-size: 12px;
	line-height: 100%;
}

.site-navigation ul li a {
	color: #000;
	text-decoration: none;
	transition: color 0.35s ease-in-out;
}

.site-navigation ul li a:hover,
.site-navigation ul li a:focus {
	color: $primary-color;
}