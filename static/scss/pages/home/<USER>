@use "../../helpers/variables" as *;

.hero {
	background-color: #F5F5F5;
	min-height: 917px;
	padding-top: 213px;
	position: relative;

	&__text-holder {
		position: relative;
		z-index: 1;
	}

	&__title {
		font-family: 'ClashDisplay-Semibold';
		font-size: 72px;
		line-height: 110%;
		color: #25324B;
		padding-bottom: 54px;
		background-image: url(../images/lines.svg);
		background-repeat: no-repeat;
		background-position: 0 100%;
		margin-bottom: 16px;
		max-width: 568px;

		span { color: $primary-color; }
	}

	&__description {
		font-size: 24px;
		line-height: 160%;
		max-width: 510px;
		margin-bottom: 36px;
	}

	&__search-form {
		display: flex;
		max-width: 852px;
		background: #fff;
		padding: 16px;
		border-radius: 30px;
		box-shadow: 0px 4px 24px 0px #0000000D;
		margin-bottom: 45px;

		input[type="text"] {
			margin: 0;
			border: 0;
			border-radius: 0;
			box-shadow: none;
			font-family: $primary-font;
			color: #000;
			font-size: 16px;
			line-height: 160%;
			outline: none;
			border-bottom: 1px solid #D6DDEB;
			flex-basis: 0;
			flex-grow: 1;

			&::placeholder {
				color: #7C8493;
				opacity: 0.5;
			}

			&::-webkit-input-placeholder {
				color: #7C8493;
				opacity: 0.5;
			}

			&:-moz-placeholder {
				color: #7C8493;
				opacity: 0.5;
			}

			&::-moz-placeholder {
				color: #7C8493;
				opacity: 0.5;
			}

			&:-ms-input-placeholder {
				color: #7C8493;
				opacity: 0.5;
			}
		}

		input[type="submit"] {
			width: 100%;
			height: 57px;
			cursor: pointer;
			font-size: 15px;
		}
	}

	&__input-holder {
		flex-grow: 1;
		flex-basis: 0;
		display: flex;
		gap: 16px;
		padding: 0 16px;
	}

	&__icon-holder {
		display: flex;
		align-items: center;
	}

	&__img-holder {
		position: absolute;
		right: 0;
		bottom: 0;

		img { vertical-align: top; }
	}

	&__jobs-available {
		position: absolute;
		right: 85px;
		top: 50%;
		backdrop-filter: blur(10px);
		box-shadow: 0px 4.76px 9.51px 0px #33303040;
		border-radius: 16px;
		background: rgba(255, 255, 255, 0.6);

		img {
			display: block;
		}
	}

	&__few-seconds {
		position: absolute;
		left: 50%;
		bottom: 20px;
		transform: translateX(-50%);
		backdrop-filter: blur(10px);
		box-shadow: 0px 4.76px 9.51px 0px #33303040;
		border-radius: 16px;

		img { display: block; }

	}
}
