.testimonials {
  padding: 70px 0;

  &__inner {
    display: flex;
    gap: 20px
  }

  &__img-holder {
    width: 41.667%;
    padding: 0 20px;
  }

  &__holder {
    padding: 0 40px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
    flex-basis: 0;
    flex-grow: 1;
  }

  &__item {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__description{
    color: #8E939C;
    font-weight: 300;

    p{ margin: 0 }
  }

  &__meta-info {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  &__img {
    border-radius: 50%;
  }

  &__meta-text-holder {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  &__by-name {
    font-weight: 400;
    font-size: 20px;
    line-height: 160%;
    margin: 0;
    color: #3A3A3A;
  }

  &__by-designation {
    font-size: 12px;
    line-height: 100%;
    color: #C4C5C8;
  }

}