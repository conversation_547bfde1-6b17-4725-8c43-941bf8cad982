@use '../../helpers/variables' as *;

.built-for-cta {
  &__inner-holder {
    border: 1px solid $primary-color;
    border-radius: 43px;
    padding: 85px 50px;
    background: linear-gradient(90.66deg, rgba(21, 101, 192, 0) 16.99%, rgba(21, 101, 192, 0.1) 95.81%), url(../images/bg-built-for.svg);
    //background: url(../images/bg-built-for.svg);
    background-repeat: no-repeat;
    background-position: top right
  }

  &__text-holder {
    max-width: 754px;
  }

  &__sub-title {
    font-size: 32px;
    line-height: 140%;
    font-family: 'ClashDisplay-Medium';
    display: block;
  }

  &__title {
    font-family: 'ClashDisplay-Semibold';
    font-size: 56px;
    line-height: 110%;
    margin-bottom: 24px;
    color: #333;
  }

  &__description {
    color: #515B6F;
    font-weight: 300;
    font-size: 20px;
    line-height: 160%;
    max-width: 600px;
  }
}