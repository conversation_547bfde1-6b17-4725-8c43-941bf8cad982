@use "../../helpers/variables" as *;

.home-hero {
	background-color: #F5F5F5;
	min-height: 917px;
	padding-top: 213px;
	position: relative;
}

.home-hero .text-holder {
	position: relative;
	z-index: 1;
}

.home-hero .title {
	font-family: 'ClashDisplay-Semibold';
	font-size: 72px;
	line-height: 110%;
	color: #25324B;
	padding-bottom: 54px;
	background-image: url(../images/lines.svg);
	background-repeat: no-repeat;
	background-position: 0 100%;
	margin-bottom: 16px;
	max-width: 568px;
}

.home-hero .title span { color: $primary-color; }

.home-hero .description {
	font-size: 24px;
	line-height: 160%;
	max-width: 510px;
	margin-bottom: 36px;
}

.hero-search-form {
	display: flex;
	max-width: 852px;
	background: #fff;
	padding: 16px;
	border-radius: 30px;
	box-shadow: 0px 4px 24px 0px #0000000D;
	margin-bottom: 45px;
}

.hero-search-form .input-holder {
	flex-grow: 1;
	flex-basis: 0;
	display: flex;
	gap: 16px;
	padding: 0 16px;
}

.hero-search-form .input-holder .icon-holder {
	display: flex;
	align-items: center;
}

.hero-search-form input[type="text"] {
	margin: 0;
	border: 0;
	border-radius: 0;
	box-shadow: none;
	font-family: $primary-font;
	color: #000;
	font-size: 16px;
	line-height: 160%;
	outline: none;
	border-bottom: 1px solid #D6DDEB;
	flex-basis: 0;
	flex-grow: 1;
}

.hero-search-form input[type="text"]::placeholder {
  color: #7C8493;
	opacity: 0.5;
}

.hero-search-form input[type="text"]::-webkit-input-placeholder {
  color: #7C8493;
	opacity: 0.5;
}

.hero-search-form input[type="text"]:-moz-placeholder {
  color: #7C8493;
	opacity: 0.5;
}

.hero-search-form input[type="text"]::-moz-placeholder {
  color: #7C8493;
	opacity: 0.5;
}

.hero-search-form input[type="text"]:-ms-input-placeholder {
  color: #7C8493;
	opacity: 0.5;
}

.hero-search-form .select2-container {
	flex-basis: 0;
	flex-grow: 1;
	align-items: center;
	border-bottom: 1px solid #D6DDEB;
}

.hero-search-form .select2-container--default .select2-selection--single {
	border: 0;
	border-radius: 0;
	height: 100%;
	display: flex;
  align-items: center;
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__rendered {
	padding: 0;
	font-family: $primary-font;
	font-size: 16px;
	line-height: 160%;
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__placeholder {
	color: #7C8493;
	opacity: 0.5;
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__arrow {
	width: 16px;
	height: 16px;
	background: url(../images/angle-down.svg) no-repeat;
	top: 50%;
	transform: translateY(-50%);
	transition: transform 0.35s ease-in-out;
}

.hero-search-form .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow {
	transform: translateY(-50%) rotate(180deg);
}

.hero-search-form .select2-container--default .select2-selection--single .select2-selection__arrow b { display: none; }

.hero-search-form .btn-submit-holder {
	width: 213px;
}

.hero-search-form input[type="submit"] {
	width: 100%;
	height: 57px;
	cursor: pointer;
	font-size: 15px;
}

.select2-dropdown {
	border: 0;
	box-shadow: 0px 4px 24px 0px #0000000D;
}

.home-hero .img-holder {
	position: absolute;
	right: 0;
	bottom: 0;
}

.home-hero .img-holder img {
	vertical-align: top;
}

.hero-jobs-available {
	position: absolute;
	right: 85px;
	top: 50%;
	backdrop-filter: blur(10px);
	box-shadow: 0px 4.76px 9.51px 0px #33303040;
	border-radius: 16px;
	background: rgba(255, 255, 255, 0.6);
}

.hero-jobs-available img {
	display: block;
}

.hero-few-seconds {
	position: absolute;
	left: 50%;
	bottom: 20px;
	transform: translateX(-50%);
	backdrop-filter: blur(10px);
	box-shadow: 0px 4.76px 9.51px 0px #33303040;
	border-radius: 16px;
}

.hero-few-seconds img { display: block; }