@use '../../helpers/variables' as *;

.how-it-works {
	padding: 60px 0;
	background: url(../images/bg-how-it-works.svg);
	background-repeat: no-repeat;

	.tab-btn-holder {
		display: flex;
		justify-content: flex-end;
	}

	.tab-btn-wrapper {
		display: flex;
		gap: 10px;
		padding: 4px;
		border-radius: 9px;
		border: 1px solid #E1E1E1;
	}

	.tab-btn {
		padding: 10px 15px;
		background: transparent;
		border: 1px solid transparent;
		display: flex;
		gap: 10px;
		align-items: center;
		color: #9CA3AF;
		font-size: 16px;
		line-height: 23px;
		margin: 0;
		cursor: pointer;
		border-radius: 7px;
		transition: all 0.35s ease-in-out;
	}

	.tab-btn svg { display: block; }

	.tab-btn.active {
		background: #1565C0;
		color: #fff;
		border-color: #0A51AC;
		box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	}

	.tab-btn.active svg path { fill: #fff; }

	.tab-content {
		padding: 80px 0 60px;

		&__pane {
			//display: flex;
			gap: 20px;
			display: none;
      opacity: 0;
      transition: opacity 0.5s ease;

			&--active {
				display: flex;
			}

			&--fade-in {
				opacity: 1;
			}
		}

		&__right {
			flex-basis: 0;
			flex-grow: 1;
		}

		&__img-holder {
			img {
				border-radius: 58px;
			}
		}

		&__left {
			flex-basis: 0;
			flex-grow: 1;
			//padding-left: 30px;
		}

		&__title-holder {
			padding-left: 49px;
		}
		
		&__sub-title {
			font-family: 'ClashDisplay-Medium';
			font-size: 24px;
			line-height: 140%;
			color: $primary-color;
			display: block;
			margin-bottom: 16px;
		}

		&__title {
			font-family: 'ClashDisplay-Semibold';
			font-size: 56px;
			line-height: 120%;
			color: #333;
			margin-bottom: 42px;

		}
	}

	.list {
		padding-left: 49px;
		border-left: 1px solid $primary-color;
		display: flex;
		flex-direction: column;
		gap: 30px;
		padding-bottom: 70px;

		&__title {
			font-weight: 500;
			font-size: 20px;
			line-height: 140%;
			margin-bottom: 10px;
			color: $primary-color;
			display: flex;
			align-items: center;
			gap: 20px;

			&--text-large {
				font-size: 32px;
				line-height: 140%;
			}
		}

		&__description {
			margin: 0;
		}
	}
}