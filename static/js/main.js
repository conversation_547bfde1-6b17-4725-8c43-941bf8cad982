document.addEventListener('DOMContentLoaded', function() {
  //TAB
  document.querySelectorAll('[data-tabs]').forEach(tabsContainer => {
    const tabButtons = tabsContainer.querySelectorAll('[data-tab-button]');
    const tabPanes = tabsContainer.querySelectorAll('[data-tab-pane]');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const targetId = button.getAttribute('data-tab-button');

        // Update active state for buttons
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Update active state for panes
        tabPanes.forEach(pane => {
          pane.classList.remove('tab-content__pane--active', 'tab-content__pane--fade-in');
          if (pane.getAttribute('data-tab-pane') === targetId) {
            pane.classList.add('tab-content__pane--active');
            setTimeout(() => pane.classList.add('tab-content__pane--fade-in'), 10);
          }
        });
      });
    });
  });
})
