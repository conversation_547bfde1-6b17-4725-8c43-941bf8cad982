# Generated by Django 4.2.21 on 2025-06-30 10:00

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="JobCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(unique=True)),
            ],
            options={
                "verbose_name_plural": "Job Categories",
            },
        ),
        migrations.CreateModel(
            name="JobListing",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=200)),
                ("location", models.CharField(max_length=100)),
                ("salary_range", models.CharField(max_length=100)),
                (
                    "employment_type",
                    models.CharField(
                        choices=[
                            ("full_time", "Full Time"),
                            ("part_time", "Part Time"),
                            ("contract", "Contract"),
                            ("internship", "Internship"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("requirements", models.TextField()),
                ("remote_work", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("deadline", models.DateField()),
                ("is_active", models.BooleanField(default=True)),
                (
                    "experience_level",
                    models.CharField(
                        choices=[
                            ("entry", "Entry Level"),
                            ("mid", "Mid Level"),
                            ("senior", "Senior Level"),
                            ("executive", "Executive"),
                        ],
                        max_length=20,
                    ),
                ),
                ("video_interview_required", models.BooleanField(default=True)),
                (
                    "interview_duration",
                    models.IntegerField(
                        default=15,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(60),
                        ],
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="jobs.jobcategory",
                    ),
                ),
                (
                    "employer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="JobApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("new", "New Application"),
                            ("reviewing", "Under Review"),
                            ("shortlisted", "Shortlisted"),
                            ("interview_scheduled", "Interview Scheduled"),
                            ("interview_completed", "Interview Completed"),
                            ("offer_made", "Offer Made"),
                            ("offer_accepted", "Offer Accepted"),
                            ("offer_declined", "Offer Declined"),
                            ("rejected", "Application Rejected"),
                            ("withdrawn", "Application Withdrawn"),
                        ],
                        default="new",
                        max_length=20,
                    ),
                ),
                ("applied_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("employer_notes", models.TextField(blank=True)),
                ("interview_date", models.DateTimeField(blank=True, null=True)),
                ("rejection_reason", models.TextField(blank=True)),
                ("cover_letter", models.TextField(blank=True)),
                ("video_interview", models.BooleanField(default=False)),
                ("video_interview_completed", models.BooleanField(default=False)),
                (
                    "applicant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "job",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jobs.joblisting",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CandidateFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "feedback_type",
                    models.CharField(
                        choices=[
                            ("technical", "Technical Skills"),
                            ("communication", "Communication Skills"),
                            ("experience", "Experience"),
                            ("cultural_fit", "Cultural Fit"),
                            ("overall", "Overall Feedback"),
                            ("interview", "Interview Performance"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "rating",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ]
                    ),
                ),
                ("comments", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jobs.jobapplication",
                    ),
                ),
            ],
            options={
                "verbose_name": "Candidate Feedback",
                "verbose_name_plural": "Candidate Feedback",
            },
        ),
    ]
