from django.db import models
from django.conf import settings
from django.urls import reverse
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models.signals import post_save
from django.dispatch import receiver
import logging
import time
import re  # Add this for regex pattern matching
from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives
from django.utils.html import strip_tags

# Add this at the top with other constants
FEEDBACK_TYPES = (
    ('technical', 'Technical Skills'),
    ('communication', 'Communication Skills'),
    ('experience', 'Experience'),
    ('cultural_fit', 'Cultural Fit'),
    ('overall', 'Overall Feedback'),
    ('interview', 'Interview Performance'),
)

logger = logging.getLogger(__name__)

class JobCategory(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    
    class Meta:
        verbose_name_plural = "Job Categories"
    
    def __str__(self):
        return self.name

class JobListing(models.Model):
    JOB_TYPES = [
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('internship', 'Internship'),
    ]
    
    EXPERIENCE_LEVELS = [
        ('entry', 'Entry Level'),
        ('mid', 'Mid Level'),
        ('senior', 'Senior Level'),
        ('executive', 'Executive'),
    ]
    
    employer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    category = models.ForeignKey(JobCategory, on_delete=models.SET_NULL, null=True)
    location = models.CharField(max_length=100)
    salary_range = models.CharField(max_length=100)
    employment_type = models.CharField(max_length=20, choices=JOB_TYPES)
    description = models.TextField()
    requirements = models.TextField()
    remote_work = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    deadline = models.DateField()
    is_active = models.BooleanField(default=True)
    experience_level = models.CharField(max_length=20, choices=EXPERIENCE_LEVELS)
    video_interview_required = models.BooleanField(default=True)
    interview_duration = models.IntegerField(
        default=15,
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )
    
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return reverse('job_detail', args=[str(self.id)])

    @property
    def interview_questions(self):
        from interviews.models import InterviewQuestion
        return InterviewQuestion.objects.filter(job=self)

    @property
    def has_interview_questions(self):
        return self.interviewquestion_set.exists()

class JobApplication(models.Model):
    # Status constants
    STATUS_NEW = 'new'
    STATUS_REVIEWING = 'reviewing'
    STATUS_SHORTLISTED = 'shortlisted'
    STATUS_INTERVIEW_SCHEDULED = 'interview_scheduled'
    STATUS_INTERVIEW_COMPLETED = 'interview_completed'
    STATUS_OFFER_MADE = 'offer_made'
    STATUS_OFFER_ACCEPTED = 'offer_accepted'
    STATUS_OFFER_DECLINED = 'offer_declined'
    STATUS_REJECTED = 'rejected'
    STATUS_WITHDRAWN = 'withdrawn'
    
    STATUS_CHOICES = [
        (STATUS_NEW, 'New Application'),
        (STATUS_REVIEWING, 'Under Review'),
        (STATUS_SHORTLISTED, 'Shortlisted'),
        (STATUS_INTERVIEW_SCHEDULED, 'Interview Scheduled'),
        (STATUS_INTERVIEW_COMPLETED, 'Interview Completed'),
        (STATUS_OFFER_MADE, 'Offer Made'),
        (STATUS_OFFER_ACCEPTED, 'Offer Accepted'),
        (STATUS_OFFER_DECLINED, 'Offer Declined'),
        (STATUS_REJECTED, 'Application Rejected'),
        (STATUS_WITHDRAWN, 'Application Withdrawn'),
    ]
    
    job = models.ForeignKey('JobListing', on_delete=models.CASCADE)
    applicant = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    applied_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    employer_notes = models.TextField(blank=True)
    interview_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    cover_letter = models.TextField(blank=True)
    video_interview = models.BooleanField(default=False)
    video_interview_completed = models.BooleanField(default=False)
    _original_status = None  # To track status changes

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_status = self.status

    @property
    def status_changed(self):
        return self.status != self._original_status

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self._original_status = self.status

    def get_status_color(self):
        status_colors = {
            'new': 'secondary',
            'reviewing': 'info',
            'shortlisted': 'primary',
            'interview_scheduled': 'warning',
            'interview_completed': 'info',
            'offer_made': 'success',
            'offer_accepted': 'success',
            'offer_declined': 'danger',
            'rejected': 'danger',
            'withdrawn': 'dark',
        }
        return status_colors.get(self.status, 'secondary')

    def __str__(self):
        return f"{self.applicant.username} - {self.job.title}"

    def _format_markdown_text(self, text):
        """Convert markdown to plain text format"""
        # Remove markdown list markers and add proper indentation
        text = re.sub(r'^[-*] ', '• ', text, flags=re.MULTILINE)
        # Remove any other markdown syntax if needed
        text = re.sub(r'[*_]{1,2}(.*?)[*_]{1,2}', r'\1', text)  # Remove bold/italic
        return text

    def send_status_update_email(self, old_status=None):
        """Send email notification when application status changes"""
        try:
            subject = f'Update on your application for {self.job.title}'
            context = {
                'applicant_name': self.applicant.get_full_name() or self.applicant.username,
                'job_title': self.job.title,
                'company_name': self.job.employer.company_name,
                'old_status': dict(JobApplication.STATUS_CHOICES).get(old_status) if old_status else None,
                'new_status': self.get_status_display(),
                'employer_name': self.job.employer.get_full_name()
            }
            
            # Render HTML version
            html_message = render_to_string('emails/application_status_update.html', context)
            plain_message = strip_tags(html_message)
            
            # Create email
            email = EmailMultiAlternatives(
                subject=subject,
                body=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[self.applicant.email]
            )
            
            # Attach HTML version
            email.attach_alternative(html_message, "text/html")
            email.send(fail_silently=False)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send status update email: {str(e)}")
            return False

class CandidateFeedback(models.Model):
    application = models.ForeignKey(JobApplication, on_delete=models.CASCADE)
    feedback_type = models.CharField(max_length=50, choices=FEEDBACK_TYPES)
    rating = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    comments = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Candidate Feedback"
        verbose_name_plural = "Candidate Feedback"
    
    def __str__(self):
        return f"{self.get_feedback_type_display()} feedback for {self.application}"
    
    def send_feedback_email(self):
        subject = f"Feedback for your application to {self.application.job.title}"
        context = {
            'feedback': self,
            'application': self.application
        }
        
        # Render HTML version
        html_message = render_to_string('emails/feedback_email.html', context)
        # Create plain text version
        plain_message = strip_tags(html_message)
        
        # Create email
        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=None,  # Uses DEFAULT_FROM_EMAIL from settings
            to=[self.application.applicant.email]
        )
        
        # Attach HTML version
        email.attach_alternative(html_message, "text/html")
        email.send() 