from django.contrib import admin
from .models import JobCategory, JobListing, JobApplication, CandidateFeedback

@admin.register(JobCategory)
class JobCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug')
    prepopulated_fields = {'slug': ('name',)}

@admin.register(JobListing)
class JobListingAdmin(admin.ModelAdmin):
    list_display = ('title', 'employer', 'location', 'employment_type', 'created_at', 'is_active')
    list_filter = ('is_active', 'employment_type', 'category')
    search_fields = ('title', 'description', 'employer__username', 'employer__company_name')
    date_hierarchy = 'created_at'

@admin.register(JobApplication)
class JobApplicationAdmin(admin.ModelAdmin):
    list_display = ('applicant', 'job', 'status', 'applied_at')
    list_filter = ('status', 'applied_at')
    search_fields = ('applicant__username', 'job__title')
    date_hierarchy = 'applied_at'

@admin.register(CandidateFeedback)
class CandidateFeedbackAdmin(admin.ModelAdmin):
    list_display = ('application', 'feedback_type', 'rating', 'created_at')
    list_filter = ('feedback_type', 'rating', 'created_at')
    search_fields = ('application__applicant__username', 'application__job__title', 'comments') 