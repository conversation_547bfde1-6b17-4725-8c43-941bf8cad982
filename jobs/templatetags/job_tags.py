from django import template
from django.http import QueryDict
from urllib.parse import urlencode

register = template.Library()

@register.simple_tag
def query_transform(query_dict, remove_param):
    """
    Returns URL-encoded query string after removing specified parameter
    """
    query = query_dict.copy()
    query.pop(remove_param, None)
    return urlencode(query)

@register.filter(name='replace')
def replace(value, arg):
    """
    Replaces all instances of arg in the value
    """
    if value:
        return value.replace(arg, " ")
    return value 