from django import forms
from .models import JobListing, JobApplication
from datetime import date

class JobListingForm(forms.ModelForm):
    deadline = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
            'min': date.today().isoformat()
        }),
        help_text="Application deadline must be a future date",
        required=True
    )

    requirements = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5,
            'placeholder': 'List job requirements and qualifications...'
        }),
        required=True,
        help_text="List all required qualifications, skills, and experience"
    )

    def clean(self):
        cleaned_data = super().clean()
        
        # Required fields validation
        required_fields = {
            'title': '⚠️ Job title is required',
            'category': '⚠️ Job category is required',
            'location': '⚠️ Job location is required',
            'salary_range': '⚠️ Salary range is required',
            'employment_type': '⚠️ Employment type is required',
            'description': '⚠️ Job description is required',
            'requirements': '⚠️ Job requirements are required',
            'experience_level': '⚠️ Experience level is required',
            'deadline': '⚠️ Application deadline is required'
        }
        
        for field, message in required_fields.items():
            if not cleaned_data.get(field):
                self.add_error(field, message)
                if field in self.fields:
                    self.fields[field].widget.attrs['class'] = self.fields[field].widget.attrs.get('class', '') + ' is-invalid'
        
        # Deadline validation
        deadline = cleaned_data.get('deadline')
        if deadline and deadline < date.today():
            self.add_error('deadline', '⚠️ Please select a future date')
            self.fields['deadline'].widget.attrs['class'] += ' is-invalid'
        
        # Validate video interview settings
        if cleaned_data.get('video_interview_required'):
            duration = cleaned_data.get('interview_duration')
            if not duration or duration < 1:
                raise forms.ValidationError(
                    "Interview duration is required for video interviews"
                )
        
        return cleaned_data

    class Meta:
        model = JobListing
        exclude = ['employer', 'created_at', 'is_active']
        help_texts = {
            'title': 'Enter a clear and specific job title',
            'salary_range': 'e.g., $50,000 - $70,000 per year',
            'description': 'Describe the role, responsibilities, and expectations',
            'requirements': 'List required qualifications and skills',
            'deadline': 'Last date to submit applications',
            'interview_duration': 'Video interview length (1-60 minutes)',
            'remote_work': 'Check if remote work is available',
        }
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g. Senior Software Engineer'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g. New York, NY'
            }),
            'salary_range': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g. $80,000 - $120,000'
            }),
            'employment_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': 'Detailed job description...'
            }),
            'requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': 'Job requirements and qualifications...'
            }),
            'experience_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'remote_work': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'interview_duration': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '60'
            })
        }

class JobApplicationForm(forms.ModelForm):
    cover_letter = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Write your cover letter here...'
        }),
        required=True
    )

    class Meta:
        model = JobApplication
        fields = ['cover_letter'] 