from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Subquery, OuterRef
from .models import JobListing, JobApplication, JobCategory
from .forms import JobListingForm, JobApplicationForm
from interviews.forms import InterviewQuestionFormSet
from interviews.models import Interview
from django.db import models
from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives
from django.utils.html import strip_tags
from django.conf import settings

def job_list(request):
    jobs = JobListing.objects.filter(is_active=True)
    
    # Get search parameters
    query = request.GET.get('q', '')
    location = request.GET.get('location', '').lower()
    employment_type = request.GET.get('employment_type', '')
    experience_level = request.GET.get('experience_level', '')
    remote = request.GET.get('remote', '')
    
    # Apply filters
    if query:
        jobs = jobs.filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(requirements__icontains=query) |
            Q(employer__company_name__icontains=query)
        )
    
    if remote == 'remote':
        jobs = jobs.filter(remote_work=True)
    elif location:
        jobs = jobs.filter(location__icontains=location)
    
    if employment_type:
        jobs = jobs.filter(employment_type=employment_type)
    
    if experience_level:
        jobs = jobs.filter(experience_level=experience_level)
    
    # Add sorting
    sort_by = request.GET.get('sort', '-created_at')
    jobs = jobs.order_by(sort_by)

    context = {
        'jobs': jobs,
        'search_query': query,
        'location_query': location,
        'employment_type': employment_type,
        'experience_level': experience_level,
        'remote': remote,
        'total_jobs': jobs.count(),
        'job_types': JobListing.JOB_TYPES,
        'experience_levels': JobListing.EXPERIENCE_LEVELS,
    }
    
    return render(request, 'jobs/job_list.html', context)

def job_detail(request, pk):
    job = get_object_or_404(JobListing, pk=pk)
    has_applied = False
    application = None
    
    if request.user.is_authenticated:
        application = JobApplication.objects.filter(
            job=job, applicant=request.user
        ).first()
        has_applied = bool(application)
    
    return render(request, 'jobs/job_detail.html', {
        'job': job,
        'has_applied': has_applied,
        'application': application,
        'interview_questions': job.interview_questions.all() if job.video_interview_required else []
    })

@login_required
def job_create(request):
    if not request.user.is_employer():
        messages.error(request, "Only employers can post jobs.")
        return redirect('home')
    
    if request.method == 'POST':
        form = JobListingForm(request.POST)
        question_formset = InterviewQuestionFormSet(request.POST)
        
        if form.is_valid() and question_formset.is_valid():
            job = form.save(commit=False)
            job.employer = request.user
            
            # Validate video interview settings
            if job.video_interview_required:
                valid_questions = [f for f in question_formset.forms if f.cleaned_data.get('question')]
                if not valid_questions:
                    form.add_error(None, "Please add at least one interview question")
                    return render(request, 'jobs/job_form.html', {
                        'form': form,
                        'question_formset': question_formset,
                        'is_edit': False
                    })
            
            # Save job and questions
            job.save()
            if job.video_interview_required:
                for i, question_form in enumerate(question_formset.forms):
                    if question_form.cleaned_data.get('question'):
                        question = question_form.save(commit=False)
                        question.job = job
                        question.order = i + 1
                        question.save()
            
            messages.success(request, '✨ Job posted successfully!')
            return redirect('jobs:job_detail', pk=job.pk)
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = JobListingForm()
        question_formset = InterviewQuestionFormSet()
    
    return render(request, 'jobs/job_form.html', {
        'form': form,
        'question_formset': question_formset,
        'is_edit': False
    })

@login_required
def job_apply(request, pk):
    if not request.user.is_jobseeker():
        messages.error(request, "Only job seekers can apply for jobs.")
        return redirect('home')
    
    job = get_object_or_404(JobListing, pk=pk)
    
    # Check if already applied
    if JobApplication.objects.filter(job=job, applicant=request.user).exists():
        messages.info(request, "You have already applied for this job.")
        return redirect('jobs:job_detail', pk=pk)
    
    # Check for interview questions first
    has_interview = job.video_interview_required
    
    if request.method == 'POST':
        form = JobApplicationForm(request.POST)
        if form.is_valid():
            application = form.save(commit=False)
            application.job = job
            application.applicant = request.user
            
            # Set video interview flag based on whether there are questions
            application.video_interview = has_interview
            application.save()
            
            if has_interview:
                messages.success(request, "Application submitted! Please complete the video interview.")
                return redirect('interviews:start_interview', application_id=application.id)
            else:
                messages.success(request, "Application submitted successfully!")
                return redirect('jobs:job_detail', pk=pk)
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = JobApplicationForm()
    
    return render(request, 'jobs/job_apply.html', {
        'job': job,
        'form': form,
        'has_interview': has_interview,
        'interview_questions': job.interview_questions.all() if has_interview else []
    })

@login_required
def employer_dashboard(request):
    if not request.user.is_employer():
        messages.error(request, "Access denied.")
        return redirect('home')
    
    # Get active jobs
    active_jobs = JobListing.objects.filter(
        employer=request.user,
        is_active=True
    ).annotate(
        applications_count=Count('jobapplication')
    ).order_by('-created_at')
    
    # Get recent applications
    recent_applications = JobApplication.objects.filter(
        job__employer=request.user
    ).select_related(
        'job', 'applicant'
    ).order_by('-applied_at')[:10]
    
    # Get statistics
    total_applications = JobApplication.objects.filter(
        job__employer=request.user
    ).count()
    
    new_applications = JobApplication.objects.filter(
        job__employer=request.user,
        status='new'
    ).count()
    
    scheduled_interviews = JobApplication.objects.filter(
        job__employer=request.user,
        status='interview_scheduled'
    ).count()
    
    offers_made = JobApplication.objects.filter(
        job__employer=request.user,
        status__in=['offer_made', 'offer_accepted']
    ).count()
    
    context = {
        'active_jobs': active_jobs,
        'recent_applications': recent_applications,
        'total_applications': total_applications,
        'new_applications': new_applications,
        'scheduled_interviews': scheduled_interviews,
        'offers_made': offers_made,
    }
    
    return render(request, 'accounts/employer_dashboard.html', context)

@login_required
def job_applications(request, pk):
    if not request.user.is_employer():
        messages.error(request, "Access denied.")
        return redirect('home')
    
    job = get_object_or_404(JobListing, pk=pk, employer=request.user)
    
    # Get applications with interview scores
    applications = JobApplication.objects.filter(job=job).select_related(
        'applicant'
    ).annotate(
        interview_score=Subquery(
            Interview.objects.filter(
                application=OuterRef('pk')
            ).values('ai_score')[:1]
        )
    )
    
    # Filter by status
    status = request.GET.get('status')
    if status:
        applications = applications.filter(status=status)
    
    # Sort applications - default to score
    sort = request.GET.get('sort', '-interview_score')  # Default sort by score
    if sort == '-interview_score':
        applications = applications.order_by('-interview_score', '-applied_at')
    elif sort in ['-applied_at', 'applied_at', 'status']:
        applications = applications.order_by(sort)
    
    context = {
        'job': job,
        'applications': applications,
        'status_choices': JobApplication.STATUS_CHOICES,
        'current_status': status,
        'current_sort': sort,
    }
    
    return render(request, 'jobs/job_applications.html', context)

@login_required
def application_detail(request, application_id):
    application = get_object_or_404(JobApplication, id=application_id)
    
    # Check permissions
    if not (request.user.is_employer() and application.job.employer == request.user) and \
       not (request.user == application.applicant):
        messages.error(request, "You don't have permission to view this application.")
        return redirect('home')
    
    context = {
        'application': application,
        'status_choices': JobApplication.STATUS_CHOICES,
    }
    return render(request, 'jobs/application_detail.html', context)

@login_required
def job_edit(request, pk):
    if not request.user.is_employer():
        messages.error(request, "Only employers can edit jobs.")
        return redirect('home')
    
    job = get_object_or_404(JobListing, pk=pk, employer=request.user)
    
    if request.method == 'POST':
        form = JobListingForm(request.POST, instance=job)
        question_formset = InterviewQuestionFormSet(request.POST, instance=job)
        
        if form.is_valid() and question_formset.is_valid():
            job = form.save()
            question_formset.save()
            messages.success(request, 'Job listing updated successfully!')
            return redirect('jobs:job_detail', pk=job.pk)
    else:
        form = JobListingForm(instance=job)
        question_formset = InterviewQuestionFormSet(instance=job)
    
    return render(request, 'jobs/job_form.html', {
        'form': form,
        'question_formset': question_formset,
        'job': job,
        'is_edit': True
    })

def job_search(request):
    query = request.GET.get('q', '')
    location = request.GET.get('location', '')
    salary_min = request.GET.get('salary_min')
    salary_max = request.GET.get('salary_max')
    experience = request.GET.get('experience')
    
    jobs = JobListing.objects.filter(is_active=True)
    
    if query:
        jobs = jobs.filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(requirements__icontains=query)
        )
    
    if location:
        jobs = jobs.filter(location__icontains=location)
    
    if salary_min and salary_max:
        jobs = jobs.filter(
            salary_range__gte=salary_min,
            salary_range__lte=salary_max
        )
    
    if experience:
        jobs = jobs.filter(experience_required=experience)
    
    # Add sorting
    sort_by = request.GET.get('sort')
    if sort_by == 'date':
        jobs = jobs.order_by('-created_at')
    elif sort_by == 'salary':
        jobs = jobs.order_by('-salary_range')
        
    return jobs

@login_required
def job_delete(request, pk):
    job = get_object_or_404(JobListing, pk=pk, employer=request.user)
    
    if request.method == 'POST':
        job.delete()
        messages.success(request, "Job posting deleted successfully!")
        return redirect('dashboard')
    
    return render(request, 'jobs/job_confirm_delete.html', {
        'job': job
    })

@login_required
def update_application_status(request, application_id):
    if not request.user.is_employer():
        messages.error(request, "Only employers can update application status.")
        return redirect('home')
        
    application = get_object_or_404(JobApplication, id=application_id)
        
    # Check if the employer owns this job
    if application.job.employer != request.user:
        messages.error(request, "You don't have permission to update this application.")
        return redirect('jobs:application_detail', application_id)
        
    if request.method == 'POST':
        new_status = request.POST.get('status')
        if new_status in dict(JobApplication.STATUS_CHOICES):
            old_status = application.status
            application.status = new_status
            application.save()
            
            # Send email notification to applicant
            if application.send_status_update_email(old_status):
                messages.success(request, f"Application status updated to {application.get_status_display()}")
            else:
                messages.warning(request, f"Status updated but failed to send email notification")
        else:
            messages.error(request, "Invalid status value")
            
    return redirect('jobs:application_detail', application_id) 