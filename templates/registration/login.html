{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Login - Job Portal{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>Welcome Back
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% if form.errors %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <div>Invalid username or password. Please try again.</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endif %}

                    {% if next %}
                        {% if user.is_authenticated %}
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <div>Your account doesn't have access to this page. Please log in with an account that has access.</div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% else %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <div>Please log in to access this page.</div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endif %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="section-card mb-4">
                            <div class="form-group mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label required">
                                    <i class="fas fa-user me-2"></i>Username
                                </label>
                                {% render_field form.username class="form-control" placeholder="Enter your username" %}
                                {% if form.username.errors %}
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ form.username.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="valid-feedback">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Looks good!
                                </div>
                            </div>

                            <div class="form-group mb-4">
                                <label for="{{ form.password.id_for_label }}" class="form-label required">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="input-group">
                                    {% render_field form.password class="form-control" placeholder="Enter your password" %}
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ form.password.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="valid-feedback">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Looks good!
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember-me" name="remember_me">
                                    <label class="form-check-label" for="remember-me">Remember me</label>
                                </div>
                                <a href="{% url 'password_reset' %}" class="text-decoration-none">
                                    <i class="fas fa-key me-1"></i>Forgot password?
                                </a>
                            </div>

                            <!-- reCAPTCHA -->
                            <div class="g-recaptcha mb-3" data-sitekey="{{ recaptcha_site_key }}"></div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </div>
                        </div>

                        <input type="hidden" name="next" value="{{ next }}">
                    </form>
                </div>
                <div class="card-footer bg-light py-3">
                    <div class="text-center">
                        <span class="text-muted">Don't have an account?</span>
                        <a href="{% url 'signup' %}" class="text-decoration-none ms-1">
                            <i class="fas fa-user-plus me-1"></i>Sign up
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.section-card {
    background-color: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-in-out;
}

.section-card:hover {
    box-shadow: 0 8px 15px rgba(79, 70, 229, 0.1);
    transform: translateY(-2px);
}

.required:after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-control {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e2e8f0;
}

.form-control:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

.form-control.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.btn-lg {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
}

.form-check-input {
    transition: all 0.3s ease-in-out;
}

.form-check-input:checked {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

.form-check-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* Enhanced card styling */
.card {
    border: none;
    background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
}

.card:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    border-bottom: none;
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.1);
}

.card-footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

/* Button styling */
.btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    border: none;
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #4f46e5 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(79, 70, 229, 0.3);
}

.btn-outline-secondary {
    border-color: #e2e8f0;
    color: #4a5568;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: #f8fafc;
    border-color: #4f46e5;
    color: #4f46e5;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
}

/* Form group styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

/* Input group styling */
.input-group {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:focus-within {
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.1);
}

/* Alert styling */
.alert {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-danger {
    background-color: #fef2f2;
    color: #dc2626;
}

.alert-info {
    background-color: #eff6ff;
    color: #1d4ed8;
}

.alert-warning {
    background-color: #fffbeb;
    color: #d97706;
}

/* Link styling */
a {
    color: #4f46e5;
    transition: all 0.3s ease;
}

a:hover {
    color: #4338ca;
    text-decoration: none;
}

/* reCAPTCHA container */
.g-recaptcha {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.g-recaptcha:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Add smooth transitions for all interactive elements */
.form-control, .btn, .form-check-input, .card, .alert {
    transition: all 0.3s ease-in-out;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input:not([type="checkbox"])');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.querySelector('input[type="password"]');

    // Password visibility toggle
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    function validateInput(input) {
        if (input.value) {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
    }

    inputs.forEach(input => {
        // Initial validation
        if (input.value) {
            validateInput(input);
        }

        // Real-time validation
        input.addEventListener('input', function() {
            validateInput(this);
        });

        // Validation on blur
        input.addEventListener('blur', function() {
            validateInput(this);
        });
    });

    // Form submission with reCAPTCHA validation
    form.addEventListener('submit', function(e) {
        const recaptchaResponse = grecaptcha.getResponse();
        if (!recaptchaResponse) {
            e.preventDefault();
            alert('Please complete the reCAPTCHA verification.');
        }
    });
});
</script>
{% endblock %} 