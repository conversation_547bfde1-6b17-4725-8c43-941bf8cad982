{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Set New Password - Job Portal{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-lock me-2"></i>Set New Password
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% if validlink %}
                        <div class="section-card mb-4">
                            <div class="alert alert-info">
                                <div class="d-flex">
                                    <i class="fas fa-info-circle me-3 mt-1"></i>
                                    <div>
                                        <h6 class="alert-heading mb-1">Create New Password</h6>
                                        <p class="mb-0">Please enter your new password twice to verify.</p>
                                    </div>
                                </div>
                            </div>

                            <form method="post" novalidate>
                                {% csrf_token %}
                                
                                <div class="form-group mb-4">
                                    <label for="{{ form.new_password1.id_for_label }}" class="form-label required">
                                        <i class="fas fa-key me-2"></i>New Password
                                    </label>
                                    {% render_field form.new_password1 class="form-control" placeholder="Enter new password" %}
                                    {% if form.new_password1.errors %}
                                        <div class="error-message">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {{ form.new_password1.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                    {% if form.new_password1.help_text %}
                                        <div class="password-requirements mt-2">
                                            <small class="text-muted">
                                                {{ form.new_password1.help_text|safe }}
                                            </small>
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-4">
                                    <label for="{{ form.new_password2.id_for_label }}" class="form-label required">
                                        <i class="fas fa-key me-2"></i>Confirm Password
                                    </label>
                                    {% render_field form.new_password2 class="form-control" placeholder="Confirm new password" %}
                                    {% if form.new_password2.errors %}
                                        <div class="error-message">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {{ form.new_password2.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-check-circle me-2"></i>Set New Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    {% else %}
                        <div class="alert alert-danger">
                            <div class="d-flex">
                                <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
                                <div>
                                    <h6 class="alert-heading mb-1">Invalid Reset Link</h6>
                                    <p class="mb-0">The password reset link was invalid, possibly because it has already been used or has expired.</p>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-4">
                            <a href="{% url 'password_reset' %}" class="btn btn-primary">
                                <i class="fas fa-redo me-2"></i>Request New Reset Link
                            </a>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light py-3">
                    <div class="text-center">
                        <span class="text-muted">Remember your password?</span>
                        <a href="{% url 'login' %}" class="text-decoration-none ms-1">
                            <i class="fas fa-sign-in-alt me-1"></i>Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.section-card {
    background-color: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.required:after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-control.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.password-requirements ul {
    padding-left: 1.5rem;
    margin-bottom: 0;
}

.password-requirements li {
    margin-bottom: 0.25rem;
}

.alert-info {
    background-color: #f0f7ff;
    border-color: #bae6fd;
    color: #0369a1;
}

.alert-info .fas {
    color: #0284c7;
}

.btn-lg {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const password1 = form.querySelector('input[name="new_password1"]');
    const password2 = form.querySelector('input[name="new_password2"]');

    function validatePassword() {
        if (password2.value && password1.value === password2.value) {
            password2.classList.add('is-valid');
            password2.classList.remove('is-invalid');
        } else if (password2.value) {
            password2.classList.remove('is-valid');
            password2.classList.add('is-invalid');
        }
    }

    if (password1 && password2) {
        password1.addEventListener('input', validatePassword);
        password2.addEventListener('input', validatePassword);
    }
});
</script>
{% endblock %} 