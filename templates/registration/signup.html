{% extends 'base.html' %}
{% load widget_tweaks %}
{% load static %}

{% block title %}Sign Up - Job Portal{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Create Your Account
                    </h4>
                </div>
                <div style="background-color: whitesmoke;" class="card-body p-4">
                    <!-- Add this at the top of the form for general errors -->
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {% for error in form.non_field_errors %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <div>{{ error }}</div>
                        </div>
                        {% endfor %}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endif %}

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% else %}fa-exclamation-circle{% endif %} me-2"></i>
                                    <div>{{ message }}</div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <!-- User Type Selection -->
                        <div class="section mb-4" data-section="1">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">1</span>
                                <i class="fas fa-user-tag me-2"></i>
                                Account Type
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="card h-100 cursor-pointer {% if form.user_type.value == 'jobseeker' %}border-primary{% endif %}">
                                                <div class="card-body p-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="user_type" 
                                                               value="jobseeker" id="jobseeker" 
                                                               checked>
                                                        <label class="form-check-label w-100" for="jobseeker">
                                                            <h5 class="mb-2">Find a Job</h5>
                                                            <p class="text-muted small mb-0">
                                                                Create your profile, apply for jobs, and complete video interviews
                                                            </p>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card h-100 cursor-pointer {% if form.user_type.value == 'employer' %}border-primary{% endif %}">
                                                <div class="card-body p-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="user_type" 
                                                               value="employer" id="employer"
                                                               {% if form.user_type.value == 'employer' %}checked{% endif %}>
                                                        <label class="form-check-label w-100" for="employer">
                                                            <h5 class="mb-2">Hire Talent</h5>
                                                            <p class="text-muted small mb-0">
                                                                Post jobs, review applications, and conduct video interviews
                                                            </p>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="section mb-4" data-section="2">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">2</span>
                                <i class="fas fa-user me-2"></i>
                                Basic Information
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6 form-group">
                                            <label for="{{ form.first_name.id_for_label }}" class="form-label required">First Name</label>
                                            {% render_field form.first_name class="form-control" placeholder="Enter your first name" autofocus="autofocus" %}
                                            {% if form.first_name.errors %}
                                            <div class="error-message">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.first_name.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                            <div class="valid-feedback">
                                                <i class="fas fa-check-circle me-1"></i>
                                                Looks good!
                                            </div>
                                            {% if form.first_name.help_text %}
                                            <div class="form-text text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                {{ form.first_name.help_text }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label required">Last Name</label>
                                            {% render_field form.last_name class="form-control" placeholder="Enter your last name" %}
                                            {% if form.last_name.errors %}
                                                <div class="invalid-feedback d-block">{{ form.last_name.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Credentials -->
                        <div class="section mb-4" data-section="3">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">3</span>
                                <i class="fas fa-lock me-2"></i>
                                Account Credentials
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label required">Username</label>
                                            {% render_field form.username class="form-control" placeholder="Choose a username" %}
                                            {% if form.username.errors %}
                                                <div class="invalid-feedback d-block">{{ form.username.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label required">Email</label>
                                            {% render_field form.email class="form-control" placeholder="Enter your email" %}
                                            {% if form.email.errors %}
                                                <div class="invalid-feedback d-block">{{ form.email.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label required">Password</label>
                                            {% render_field form.password1 class="form-control" placeholder="Create a password" %}
                                            {% if form.password1.errors %}
                                                <div class="invalid-feedback d-block">{{ form.password1.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label required">Confirm Password</label>
                                            {% render_field form.password2 class="form-control" placeholder="Confirm your password" %}
                                            {% if form.password2.errors %}
                                                <div class="invalid-feedback d-block">{{ form.password2.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Information -->
                        <div id="jobseeker-fields" class="section mb-4">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">4</span>
                                <i class="fas fa-briefcase me-2"></i>
                                Professional Information
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Years of Experience</label>
                                            {% render_field form.experience_years class="form-control" type="number" min="0" %}
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Skills</label>
                                            {% render_field form.skills class="form-control" rows="3" placeholder="Enter your skills (e.g., Python, Project Management, Marketing)" %}
                                        </div>
                                    </div>
                                    <div class="row g-3 mb-4">
                                        <!-- Profile Image Upload -->
                                        <div class="col-md-6">
                                            <label class="form-label">Profile Photo</label>
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="profile-upload-preview">
                                                    <img id="profilePreview" 
                                                         src="{% static 'img/default-avatar.png' %}" 
                                                         class="rounded-circle" 
                                                         alt="Profile preview">
                                                    <div class="upload-overlay">
                                                        <i class="fas fa-camera"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="custom-file-upload">
                                                        {{ form.profile_image }}
                                                        <label for="{{ form.profile_image.id_for_label }}" class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-upload me-2"></i>Choose Photo
                                                        </label>
                                                    </div>
                                                    <div class="form-text">{{ form.profile_image.help_text }}</div>
                                                    {% if form.profile_image.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.profile_image.errors }}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Resume Upload -->
                                        <div class="col-md-6">
                                            <label class="form-label">Resume</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-file-pdf"></i>
                                                </span>
                                                {{ form.resume|add_class:"form-control" }}
                                            </div>
                                            <div class="form-text">{{ form.resume.help_text }}</div>
                                            {% if form.resume.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.resume.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Company Information -->
                        <div id="employer-fields" class="section mb-4 d-none">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">4</span>
                                <i class="fas fa-building me-2"></i>
                                Company Information
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label required">Company Name</label>
                                            {% render_field form.company_name class="form-control" placeholder="Enter your company name" %}
                                            {% if form.company_name.errors %}
                                                <div class="invalid-feedback d-block">
                                                    <i class="fas fa-exclamation-circle me-1"></i>
                                                    {{ form.company_name.errors|join:", " }}
                                                </div>
                                            {% endif %}
                                            <div class="valid-feedback">
                                                <i class="fas fa-check-circle me-1"></i>
                                                Looks good!
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Company Website</label>
                                            {% render_field form.company_website class="form-control" placeholder="https://www.example.com" %}
                                            {% if form.company_website.errors %}
                                                <div class="invalid-feedback d-block">{{ form.company_website.errors|join:", " }}</div>
                                            {% endif %}
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Include full URL (e.g., https://www.example.com)
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Company Description</label>
                                            {% render_field form.company_description class="form-control" rows="3" placeholder="Brief description of your company" %}
                                            {% if form.company_description.errors %}
                                                <div class="invalid-feedback d-block">{{ form.company_description.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                        <!-- Company Logo Upload -->
                                        <div class="col-md-6">
                                            <label class="form-label">Company Logo</label>
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="profile-upload-preview">
                                                    <img id="companyLogoPreview" 
                                                         src="{% static 'img/default-company-logo.png' %}" 
                                                         class="rounded-circle" 
                                                         alt="Company logo preview">
                                                    <div class="upload-overlay">
                                                        <i class="fas fa-camera"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="custom-file-upload">
                                                        {{ form.company_logo }}
                                                        <label for="{{ form.company_logo.id_for_label }}" class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-upload me-2"></i>Upload Logo
                                                        </label>
                                                    </div>
                                                    <div class="form-text">Upload your company logo (optional)</div>
                                                    {% if form.company_logo.errors %}
                                                        <div class="invalid-feedback d-block">
                                                            {{ form.company_logo.errors }}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{% url 'login' %}" class="btn btn-link text-muted">
                                Already have an account? Login
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <span class="btn-content">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </span>
                                <span class="btn-loader d-none">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    Creating...
                                </span>
                            </button>
                        </div>
                    </form>

                    <!-- Loading Overlay -->
                    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" 
                         style="background: rgba(255,255,255,0.8); z-index: 9999;">
                        <div class="position-absolute top-50 start-50 translate-middle text-center">
                            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                            <h5 class="mb-0">Creating your account...</h5>
                            <p class="text-muted">Please wait while we set everything up</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.section-title {
    color: #1a202c;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
}

.section-number {
    width: 28px;
    height: 28px;
    background-color: #4f46e5;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    margin-right: 0.75rem;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.cursor-pointer {
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

.cursor-pointer:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.section-card {
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    background: #ffffff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-in-out;
    margin-bottom: 1.5rem;
}

.section-card:hover {
    box-shadow: 0 8px 15px rgba(79, 70, 229, 0.1);
    transform: translateY(-2px);
}

.section.active .section-card {
    border-color: #4f46e5;
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.15);
}

.form-label {
    font-weight: 500;
    color: #4a5568;
}

.form-control:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

/* Add these validation styles */
.form-group {
    margin-bottom: 1rem;
}

.required:after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

.form-label.required {
    font-weight: 500;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.form-check-input.is-invalid {
    border-color: #dc3545;
}

.form-check-input.is-invalid:focus {
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.section-card.has-error {
    border-color: #dc3545;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
}

/* Add these styles for valid state */
.form-control.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.section-card.is-valid {
    border-color: #198754;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.1);
}

.valid-feedback {
    display: none;
    color: #198754;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-control.is-valid ~ .valid-feedback {
    display: block;
}

/* Profile image styles */
.profile-upload-preview {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e9ecef;
    background-color: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.profile-upload-preview:hover {
    border-color: #4f46e5;
    box-shadow: 0 6px 12px rgba(79, 70, 229, 0.2);
    transform: translateY(-2px);
}

.profile-upload-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-upload-preview .upload-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0));
    color: white;
    padding: 15px 0 8px;
    font-size: 14px;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.profile-upload-preview:hover .upload-overlay {
    opacity: 1;
}

.custom-file-upload input[type="file"] {
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
}

/* Section card hover and focus states */
.section-card {
    transition: all 0.3s ease-in-out;
    border: 1px solid #dee2e6;
    padding: 1.5rem;
}

.section-card:hover {
    border-color: #4f46e5;
    box-shadow: 0 8px 16px rgba(79, 70, 229, 0.15);
}

.section-card:focus-within {
    border-color: #4f46e5;
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.2);
}

/* Active section indicator */
.section.active .section-card {
    border-color: #4f46e5;
    box-shadow: 0 12px 24px rgba(79, 70, 229, 0.2);
}

.section.active .section-number {
    background-color: #4f46e5;
    color: white;
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.card-header {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    border-bottom: none;
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.1);
}

.cursor-pointer.border-primary {
    border-color: #4f46e5 !important;
    background-color: #f5f3ff;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    border: none;
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #4f46e5 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(79, 70, 229, 0.3);
}

.form-control:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* Add smooth transitions for all interactive elements */
.form-control, .btn, .form-check-input, .card {
    transition: all 0.3s ease-in-out;
}

/* Enhanced card styling */
.card {
    border: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
}

.card:hover {
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

/* Section spacing and organization */
.section {
    margin-bottom: 2rem;
    transition: all 0.3s ease-in-out;
}

.section:last-child {
    margin-bottom: 0;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];
    
    if (file) {
        // Check file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file');
            input.value = '';
            return;
        }
        
        // Check file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Please select an image less than 5MB');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
        }
        reader.readAsDataURL(file);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const userTypeInputs = document.querySelectorAll('input[name="user_type"]');
    const jobseekerFields = document.getElementById('jobseeker-fields');
    const employerFields = document.getElementById('employer-fields');
    const cards = document.querySelectorAll('.cursor-pointer');

    // Function to focus on first input field of the form
    function focusFirstField() {
        const firstNameInput = document.querySelector('input[name="first_name"]');
        
        // Scroll to top of the form smoothly
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        setTimeout(() => {
            firstNameInput?.focus();
        }, 100); // Small delay to ensure smooth transition
    }

    // Make entire cards clickable
    cards.forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            radio.dispatchEvent(new Event('change'));
        });
    });

    userTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Remove border from all cards
            cards.forEach(card => card.classList.remove('border-primary'));
            // Add border to selected card
            this.closest('.card').classList.add('border-primary');

            if (this.value === 'jobseeker') {
                jobseekerFields.classList.remove('d-none');
                employerFields.classList.add('d-none');
                focusFirstField();
            } else {
                jobseekerFields.classList.add('d-none');
                employerFields.classList.remove('d-none');
                focusFirstField();
            }
        });
    });

    // Enhanced validation handling
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select, textarea');

    function validateInput(input) {
        const section = input.closest('.section-card');
        
        if (input.checkValidity()) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            if (section) {
                section.classList.remove('has-error');
                // Check if all inputs in section are valid
                const sectionInputs = section.querySelectorAll('input, select, textarea');
                const allValid = Array.from(sectionInputs).every(inp => inp.checkValidity());
                if (allValid) {
                    section.classList.add('is-valid');
                }
            }
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            if (section) {
                section.classList.remove('is-valid');
                section.classList.add('has-error');
            }
        }
    }

    inputs.forEach(input => {
        // Skip radio buttons and checkboxes for now
        if (input.type !== 'radio' && input.type !== 'checkbox') {
            // Initial validation state
            if (input.value) {
                validateInput(input);
            }

            // Handle invalid event
            input.addEventListener('invalid', function(e) {
                e.preventDefault();
                validateInput(this);
            });

            // Real-time validation on input
            input.addEventListener('input', function() {
                validateInput(this);
            });

            // Validation on blur
            input.addEventListener('blur', function() {
                if (this.value) {
                    validateInput(this);
                }
            });
        }
    });

    // Special handling for password confirmation
    const password1 = form.querySelector('input[name="password1"]');
    const password2 = form.querySelector('input[name="password2"]');
    
    if (password1 && password2) {
        password2.addEventListener('input', function() {
            if (password1.value === this.value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    }

    // Handle form submission
    form.addEventListener('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            // Show loading state only if form is valid
            const submitBtn = this.querySelector('button[type="submit"]');
            const btnContent = submitBtn.querySelector('.btn-content');
            const btnLoader = submitBtn.querySelector('.btn-loader');
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            submitBtn.disabled = true;
            btnContent.classList.add('d-none');
            btnLoader.classList.remove('d-none');
            loadingOverlay.classList.remove('d-none');
        }
    });

    // Profile image handling
    const profileInput = document.querySelector('input[name="profile_image"]');
    const profilePreview = document.querySelector('.profile-upload-preview');
    
    if (profileInput && profilePreview) {
        profileInput.addEventListener('change', function() {
            previewImage(this, 'profilePreview');
        });

        profilePreview.addEventListener('click', function() {
            profileInput.click();
        });
    }

    // Company logo handling
    const logoInput = document.querySelector('input[name="company_logo"]');
    const logoPreview = document.getElementById('companyLogoPreview')?.closest('.profile-upload-preview');
    
    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function() {
            previewImage(this, 'companyLogoPreview');
        });

        logoPreview.addEventListener('click', function() {
            logoInput.click();
        });
    }

    // Update company section visibility
    userTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.value === 'employer') {
                employerFields.classList.remove('d-none');
                employerFields.classList.add('active');
                jobseekerFields.classList.add('d-none');
                jobseekerFields.classList.remove('active');
                companyNameInput.setAttribute('required', '');
                validateInput(companyNameInput);
            } else {
                employerFields.classList.add('d-none');
                employerFields.classList.remove('active');
                jobseekerFields.classList.remove('d-none');
                jobseekerFields.classList.add('active');
                companyNameInput.removeAttribute('required');
                companyNameInput.classList.remove('is-invalid', 'is-valid');
            }
        });
    });

    // Handle section highlighting
    const sections = document.querySelectorAll('.section');
    
    function updateActiveSections() {
        sections.forEach(section => {
            const inputs = section.querySelectorAll('input, select, textarea');
            const isFocused = Array.from(inputs).some(input => document.activeElement === input);
            if (isFocused) {
                section.classList.add('active');
            } else {
                section.classList.remove('active');
            }
        });
    }

    // Add focus and blur events to all inputs
    document.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('focus', updateActiveSections);
        input.addEventListener('blur', () => {
            setTimeout(updateActiveSections, 100);
        });
    });
});
</script>
{% endblock %} 