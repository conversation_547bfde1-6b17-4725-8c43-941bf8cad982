{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Reset Password - Job Portal{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% if form.errors %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <div>{{ form.email.errors|join:", " }}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endif %}

                    <div class="section-card mb-4">
                        <div class="alert alert-info">
                            <div class="d-flex">
                                <i class="fas fa-info-circle me-3 mt-1"></i>
                                <div>
                                    <h6 class="alert-heading mb-1">Password Reset Instructions</h6>
                                    <p class="mb-0">Enter your email address below and we'll send you instructions to reset your password.</p>
                                </div>
                            </div>
                        </div>

                        <form method="post" novalidate>
                            {% csrf_token %}
                            <div class="form-group mb-4">
                                <label for="{{ form.email.id_for_label }}" class="form-label required">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                {% render_field form.email class="form-control" placeholder="Enter your email address" %}
                                <div class="valid-feedback">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Looks good!
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-footer bg-light py-3">
                    <div class="text-center">
                        <span class="text-muted">Remember your password?</span>
                        <a href="{% url 'login' %}" class="text-decoration-none ms-1">
                            <i class="fas fa-sign-in-alt me-1"></i>Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.section-card {
    background-color: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.required:after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

.form-control.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.alert-info {
    background-color: #f0f7ff;
    border-color: #bae6fd;
    color: #0369a1;
}

.alert-info .fas {
    color: #0284c7;
}

.btn-lg {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const emailInput = form.querySelector('input[type="email"]');

    function validateEmail(input) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (input.value && emailRegex.test(input.value)) {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
    }

    if (emailInput) {
        emailInput.addEventListener('input', function() {
            validateEmail(this);
        });

        emailInput.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
});
</script>
{% endblock %} 