{% extends 'base.html' %}
{% load static %}

{% block title %}Interview Attempts{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Interview Recordings</h4>
            {% if can_record_more %}
            <a href="{% url 'interviews:record_response' interview.id %}" id="recordNewAttempt" class="btn btn-light">
                <i class="fas fa-video me-2"></i>Record New Attempt
            </a>
            {% endif %}
        </div>
        <div class="card-body">
            {% if attempts %}
                <form method="post" id="attemptsForm">
                    {% csrf_token %}
                    <div class="row">
                        {% for attempt in attempts %}
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 {% if attempt.is_selected %}border-primary{% endif %}">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        Attempt #{{ forloop.counter }}
                                        {% if attempt.is_selected %}
                                        <span class="badge bg-primary ms-2">Selected</span>
                                        {% endif %}
                                    </h5>
                                    <p class="text-muted">{{ attempt.created_at|date:"F j, Y, g:i a" }}</p>
                                    
                                    <div class="video-preview mb-3">
                                        <video src="{{ attempt.video_file.url }}" 
                                               controls 
                                               class="w-100" 
                                               style="border-radius: 8px;">
                                        </video>
                                    </div>

                                    {% if attempt.notes %}
                                    <div class="notes-section mb-3">
                                        <h6>Notes:</h6>
                                        <p class="mb-0">{{ attempt.notes }}</p>
                                    </div>
                                    {% endif %}

                                    <div class="form-check">
                                        <input type="radio" 
                                               name="selected_attempt" 
                                               value="{{ attempt.id }}"
                                               class="form-check-input"
                                               {% if attempt.is_selected %}checked{% endif %}
                                               onchange="this.form.submit()">
                                        <label class="form-check-label">Select as Best Take</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-4">
                        <button type="button" 
                                id="submitForAnalysis" 
                                class="btn btn-success btn-lg"
                                {% if not has_selected %}disabled{% endif %}>
                            <i class="fas fa-check-circle me-2"></i>Submit Selected Recording for Analysis
                        </button>
                    </div>
                </form>
                <div id="statusDiv" class="mt-3"></div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h5>No Recordings Yet</h5>
                    <p class="text-muted">Start by recording your first interview attempt.</p>
                    <a href="{% url 'interviews:record_response' interview.id %}" class="btn btn-primary">
                        <i class="fas fa-video me-2"></i>Start Recording
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const submitBtn = document.getElementById('submitForAnalysis');
    const statusDiv = document.getElementById('statusDiv');
    const recordNewAttempt = document.getElementById('recordNewAttempt');
    
    // Enable submit button when a recording is selected
    document.querySelectorAll('input[name="selected_attempt"]').forEach(radio => {
        radio.addEventListener('change', function() {
            submitBtn.disabled = false;
        });
    });
    
    submitBtn.addEventListener('click', async function() {
        submitBtn.disabled = true;
        // Properly disable the "Record New Attempt" link
        recordNewAttempt.classList.add('disabled');
        recordNewAttempt.setAttribute('aria-disabled', 'true');
        recordNewAttempt.addEventListener('click', function(e) {
            e.preventDefault();
        });
        statusDiv.innerHTML = `
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    Submitting recording for analysis...
                </div>
            </div>
        `;
        
        try {
            const response = await fetch("{% url 'interviews:submit_interview' interview.id %}", {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            if (data.success) {
                statusDiv.innerHTML = `
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h4 class="alert-heading">Interview Submitted Successfully!</h4>
                        <p>Your video interview has been submitted and is being analyzed. You’ll be redirected to the dashboard shortly.</p>
                        <div class="mt-3">
                            <a href="/dashboard/" class="btn btn-primary">
                                Go to Dashboard
                            </a>
                        </div>
                    </div>
                `;
                await new Promise(resolve => setTimeout(resolve, 5000));

                // If a redirect_url is provided, redirect to it
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                    return;
                }
                
            } else {
                throw new Error(data.error || 'Submission failed');
            }
        } catch (error) {
            console.error('Submission error:', error);
            statusDiv.innerHTML = `
                <div class="alert alert-danger">
                    Error submitting interview: ${error.message}
                </div>
            `;
            submitBtn.disabled = false;
        }
    });
});
</script>
{% endblock %} 