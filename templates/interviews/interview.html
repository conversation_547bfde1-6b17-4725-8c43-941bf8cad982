{% extends 'base.html' %}
{% load static %}
{% load interview_filters %}

{% block title %}Video Interview - Attempt {{ attempt_number }}/{{ max_attempts }}{% endblock %}

{% block extra_css %}
<style>
    .video-container {
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }
    
    .video-preview {
        width: 100%;
        border: 2px solid #ddd;
        border-radius: 8px;
        background-color: #f8f9fa;
    }
    
    .timer {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-size: 1.2rem;
    }
    
    .attempt-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
    }
    
    .recording-indicator {
        position: absolute;
        top: 4rem;
        right: 1rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #dc3545;
        display: none;
    }
    
    .recording-indicator.active {
        display: block;
        animation: blink 1s infinite;
    }

    .notes-section {
        margin-top: 1rem;
        display: none;
    }
    
    @keyframes blink {
        50% { opacity: 0; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Video Interview</h4>
            <span class="badge bg-light text-primary">
                Attempt {{ attempt_number }}/{{ max_attempts }} 
                ({{ attempts_left }} remaining)
            </span>
        </div>
        <div class="card-body">
            <!-- Questions Section -->
            <div class="questions-section mb-4">
                <h5>Interview Questions:</h5>
                {% for question in questions %}
                <div class="question-card">
                    <strong>Q{{ forloop.counter }}:</strong> {{ question.question }}
                </div>
                {% endfor %}
            </div>

            <!-- Video Recording Section -->
            <div class="video-container mb-4">
                <video id="preview" autoplay muted class="video-preview"></video>
                <div id="timer" class="timer" style="display: none;">00:00</div>
                <div class="recording-indicator"></div>
                <div class="attempt-badge">Attempt {{ attempt_number }}/{{ max_attempts }}</div>
            </div>

            <div class="controls text-center mb-3">
                <button id="startButton" class="btn btn-primary">
                    <i class="fas fa-video me-2"></i>Start Recording
                </button>
                <button id="stopButton" class="btn btn-danger" style="display: none;">
                    <i class="fas fa-stop me-2"></i>Stop Recording
                </button>
            </div>

            <!-- Notes Section -->
            <div id="notesSection" class="notes-section">
                <h5>Add Notes (Optional)</h5>
                <textarea id="recordingNotes" class="form-control" rows="3" 
                        placeholder="Add any notes about this recording attempt..."></textarea>
            </div>

            <div id="statusDiv" class="mt-3"></div>

            <div class="mt-4">
                <a href="{% url 'interviews:view_attempts' interview.id %}" 
                   class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Attempts
                </a>
                <button id="submitButton" class="btn btn-success" disabled>
                    <i class="fas fa-check me-2"></i>Submit Recording
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let mediaRecorder;
let recordedChunks = [];
let startTime;
let timerInterval;

async function initCamera() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        document.getElementById('preview').srcObject = stream;
        mediaRecorder = new MediaRecorder(stream);
        
        mediaRecorder.ondataavailable = (e) => {
            if (e.data.size > 0) {
                recordedChunks.push(e.data);
            }
        };
        
        mediaRecorder.onstop = () => {
            document.getElementById('submitButton').disabled = false;
        };
        
    } catch (err) {
        console.error('Error accessing camera:', err);
        document.getElementById('statusDiv').innerHTML = `
            <div class="alert alert-danger">
                Error accessing camera: ${err.message}
            </div>
        `;
    }
}

function startRecording() {
    recordedChunks = [];
    startTime = Date.now();
    mediaRecorder.start();
    
    document.querySelector('.recording-indicator').classList.add('active');
    document.getElementById('timer').style.display = 'block';
    document.getElementById('startButton').style.display = 'none';
    document.getElementById('stopButton').style.display = 'inline-block';
    
    startTimer();
}

function stopRecording() {
    mediaRecorder.stop();
    clearInterval(timerInterval);
    
    document.querySelector('.recording-indicator').classList.remove('active');
    document.getElementById('stopButton').style.display = 'none';
    document.getElementById('notesSection').style.display = 'block';
}

function startTimer() {
    const timerDisplay = document.getElementById('timer');
    timerInterval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

async function submitRecording() {
    const formData = new FormData();
    const blob = new Blob(recordedChunks, { type: 'video/webm' });
    formData.append('video', blob, 'interview.webm');
    formData.append('notes', document.getElementById('recordingNotes').value);
    
    const statusDiv = document.getElementById('statusDiv');
    statusDiv.innerHTML = `
        <div class="alert alert-info">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                Uploading recording...
            </div>
        </div>
    `;
    
    try {
        const response = await fetch("{% url 'interviews:save_attempt' interview.id %}", {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': '{{ csrf_token }}'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            window.location.href = "{% url 'interviews:view_attempts' interview.id %}";
        } else {
            throw new Error(data.error || 'Upload failed');
        }
    } catch (error) {
        console.error('Upload error:', error);
        statusDiv.innerHTML = `
            <div class="alert alert-danger">
                Error uploading recording: ${error.message}
            </div>
        `;
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initCamera();
    document.getElementById('startButton').addEventListener('click', startRecording);
    document.getElementById('stopButton').addEventListener('click', stopRecording);
    document.getElementById('submitButton').addEventListener('click', submitRecording);
});
</script>
{% endblock %} 