{% extends 'base.html' %}

{% block title %}Video Interview - {{ application.job.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Video Interview Questions</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Please answer all questions in your video interview. You'll have {{ application.job.interview_duration }} minutes to complete all responses.
            </div>

            <div class="questions-list mb-4">
                <h5>Questions:</h5>
                <ol>
                    {% for question in questions %}
                        <li class="mb-2">{{ question.question }}</li>
                    {% endfor %}
                </ol>
            </div>

            <div class="text-center">
                <a href="{% url 'interviews:record_response' interview.id %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-video me-2"></i>Start Interview
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 