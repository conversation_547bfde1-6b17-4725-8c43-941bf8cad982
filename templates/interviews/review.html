{% extends 'base.html' %}

{% block title %}Review Interview - {{ application.applicant.username }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <h2 class="mb-4">Review Interview Responses</h2>
            <div class="card mb-4">
                <div class="card-body">
                    <h5>Applicant Information</h5>
                    <p><strong>Name:</strong> {{ application.applicant.get_full_name }}</p>
                    <p><strong>Position:</strong> {{ application.job.title }}</p>
                    <p><strong>Applied:</strong> {{ application.applied_at|date:"F j, Y" }}</p>
                </div>
            </div>

            {% for response in responses %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Question {{ forloop.counter }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-3"><strong>Question:</strong> {{ response.question.question }}</p>
                    
                    <div class="video-container mb-3">
                        <video controls class="w-100">
                            <source src="{{ response.video_file.url }}" type="video/webm">
                            Your browser does not support the video tag.
                        </video>
                    </div>

                    <!-- AI Analysis Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-robot me-2"></i>AI Analysis
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if response.analysis_status == 'completed' %}
                                <div class="analysis-content">
                                    <h6>Confidence and Body Language:</h6>
                                    <p>{{ response.ai_analysis.analysis.confidence }}</p>
                                    
                                    <h6>Communication Skills:</h6>
                                    <p>{{ response.ai_analysis.analysis.communication }}</p>
                                    
                                    <h6>Professional Appearance:</h6>
                                    <p>{{ response.ai_analysis.analysis.appearance }}</p>
                                    
                                    <h6>Areas for Improvement:</h6>
                                    <p>{{ response.ai_analysis.analysis.improvements }}</p>
                                </div>
                            {% elif response.analysis_status == 'processing' %}
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Analysis in progress...</p>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    Analysis not yet started or failed. Please check back later.
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <form class="feedback-form" method="post" action="{% url 'save_feedback' response.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="feedback-{{ response.id }}" class="form-label">Feedback</label>
                            <textarea class="form-control" id="feedback-{{ response.id }}" 
                                      name="feedback" rows="3">{{ response.feedback }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Save Feedback</button>
                    </form>
                </div>
            </div>
            {% endfor %}

            <div class="card">
                <div class="card-body">
                    <h5>Update Application Status</h5>
                    <form method="post" action="{% url 'update_application_status' application.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <select name="status" class="form-select">
                                {% for value, label in application.STATUS_CHOICES %}
                                <option value="{{ value }}" 
                                        {% if application.status == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 