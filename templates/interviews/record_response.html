{% extends 'base.html' %}
{% load static %}

{% block title %}Record Interview - {{ interview.application.job.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Video Interview Recording</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                You have {{ duration }} minutes to complete your video responses.
            </div>

            <div class="questions-list mb-4">
                <h5>Interview Questions:</h5>
                <ol>
                    {% for question in questions %}
                        <li class="mb-3">{{ question.question }}</li>
                    {% endfor %}
                </ol>
            </div>

            <div class="video-recorder">
                <div id="video-preview" class="mb-3">
                    <video id="preview" width="100%" height="auto" autoplay muted></video>
                </div>

                <div class="controls text-center">
                    <button id="startButton" class="btn btn-primary btn-lg">
                        <i class="fas fa-video me-2"></i>Start Recording
                    </button>
                    <button id="stopButton" class="btn btn-danger btn-lg" style="display: none;">
                        <i class="fas fa-stop me-2"></i>Stop Recording
                    </button>
                </div>

                <div id="timer" class="text-center mt-3" style="display: none;">
                    <h3>Time Remaining: <span id="time">00:00</span></h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add your video recording JavaScript here
// This will handle the video recording functionality
</script>
{% endblock %} 