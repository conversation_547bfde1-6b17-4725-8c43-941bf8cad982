{% extends 'base.html' %}
{% load static %}

{% block title %}Interview Analysis{% endblock %}

{% block extra_css %}
<style>
    .analysis-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .analysis-header {
        background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }

    .analysis-section {
        border-left: 4px solid #4f46e5;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 0 8px 8px 0;
    }

    .strength-item {
        background-color: #ecfdf5;
        border-left: 4px solid #059669;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0 4px 4px 0;
        color: #065f46;
    }

    .improvement-item {
        background-color: #fef2f2;
        border-left: 4px solid #dc2626;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0 4px 4px 0;
        color: #991b1b;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
    }

    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .markdown-content {
        font-size: 1rem;
        line-height: 1.6;
    }

    .markdown-content h2 {
        color: #1e293b;
        font-size: 1.5rem;
        margin-top: 2rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e2e8f0;
    }

    .markdown-content h3 {
        color: #334155;
        font-size: 1.25rem;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .markdown-content ul, .markdown-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    .markdown-content li {
        margin-bottom: 0.5rem;
    }

    .video-section {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 2rem;
    }

    .video-container {
        position: relative;
        background: #f8f9fa;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .video-container video {
        display: block;
        max-height: 400px;
        width: 100%;
        margin: 0 auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card analysis-card">
                <div class="analysis-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Interview Analysis</h4>
                        <span class="status-badge bg-{{ interview.get_status_color }}">
                            {{ interview.get_analysis_status_display }}
                        </span>
                    </div>
                    <p class="mb-0 mt-2 text-white-50">
                        {{ job.title }} - {{ application.applicant.get_full_name }}
                    </p>
                </div>

                <div class="card-body">
                    <!-- Add Video Player Section -->
                    {% if selected_video %}
                    <div class="video-section mb-4">
                        <h5>Interview Recording</h5>
                        <video controls class="w-100 rounded">
                            <source src="{{ selected_video.video_file.url }}" type="video/webm">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    {% endif %}

                    {% if interview.analysis_status == 'completed' %}
                        <!-- Questions and Answers -->
                        <div class="questions-section mb-4">
                            <h5>Interview Questions</h5>
                            <div class="list-group">
                                {% for question in questions %}
                                    <div class="list-group-item">
                                        <h6 class="mb-1">Q{{ forloop.counter }}: {{ question.question }}</h6>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- AI Analysis -->
                        <div class="analysis-section">
                            <h5>AI Analysis</h5>
                            <div class="markdown-content">
                                {{ interview.ai_analysis|safe }}
                            </div>
                        </div>

                        <!-- Score Section -->
                        {% if interview.ai_score %}
                            <div class="score-section text-center mt-4">
                                <h5>Overall Score</h5>
                                <div class="score-badge 
                                    {% if interview.ai_score >= 8 %}bg-success
                                    {% elif interview.ai_score >= 6 %}bg-primary
                                    {% else %}bg-warning{% endif %}">
                                    {{ interview.ai_score }}/10
                                </div>
                            </div>
                        {% endif %}

                        <div class="mt-4 text-center">
                            <a href="{% url 'jobs:application_detail' application.id %}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-user me-2"></i>View Application
                            </a>
                            <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>

                    {% elif interview.analysis_status == 'processing' %}
                        <div class="loading-spinner">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted mb-0">Analyzing interview responses...</p>
                                <small class="text-muted">This may take a few minutes</small>
                            </div>
                        </div>
                    {% elif interview.analysis_status == 'pending' %}
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">Analysis Pending</h5>
                            <p class="mb-0">Applicant interview analysis is pending. Please check back soon.</p>
                        </div>
                    {% else %}
                        <div class="alert alert-danger">
                            <h5 class="alert-heading">Analysis Failed</h5>
                            <p class="mb-0">{{ interview.analysis_error|default:"An unknown error occurred" }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 