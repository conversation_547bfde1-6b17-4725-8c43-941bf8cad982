{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4>Test Video Analysis</h4>
                </div>
                <div class="card-body">
                    <form id="uploadForm" method="POST" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="video" class="form-label">Select Video File</label>
                            <input type="file" class="form-control" id="video" name="video_response" accept="video/mp4,video/webm">
                        </div>
                        <button type="submit" class="btn btn-primary">Upload and Analyze</button>
                    </form>

                    <div id="analysisResult" class="mt-4" style="display: none;">
                        <h5>Analysis Result:</h5>
                        <pre id="resultText" class="bg-light p-3 rounded"></pre>
                    </div>

                    <div id="loadingIndicator" class="text-center mt-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Analyzing video... This may take a few minutes.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const loadingIndicator = document.getElementById('loadingIndicator');
    const analysisResult = document.getElementById('analysisResult');
    const resultText = document.getElementById('resultText');
    
    loadingIndicator.style.display = 'block';
    analysisResult.style.display = 'none';
    
    fetch('{% url "interviews:submit_interview" test_interview.id %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        loadingIndicator.style.display = 'none';
        analysisResult.style.display = 'block';
        
        if (data.status === 'success') {
            // Poll for analysis results
            checkAnalysisStatus();
        } else {
            resultText.textContent = 'Error: ' + data.message;
        }
    })
    .catch(error => {
        loadingIndicator.style.display = 'none';
        analysisResult.style.display = 'block';
        resultText.textContent = 'Error: ' + error;
    });
});

function checkAnalysisStatus() {
    fetch('{% url "interviews:check_analysis_status" test_interview.id %}')
    .then(response => response.json())
    .then(data => {
        const resultText = document.getElementById('resultText');
        const loadingIndicator = document.getElementById('loadingIndicator');
        
        switch(data.status) {
            case 'completed':
                loadingIndicator.style.display = 'none';
                resultText.textContent = data.analysis;
                break;
            case 'processing':
                loadingIndicator.style.display = 'block';
                resultText.textContent = 'Processing... Please wait.';
                setTimeout(checkAnalysisStatus, 10000); // Check every 10 seconds
                break;
            case 'failed':
                loadingIndicator.style.display = 'none';
                resultText.textContent = 'Analysis failed: ' + data.error;
                break;
            default:
                loadingIndicator.style.display = 'none';
                resultText.textContent = 'Unknown status: ' + data.status;
        }
    })
    .catch(error => {
        document.getElementById('resultText').textContent = 'Error checking status: ' + error;
    });
}
</script>
{% endblock %} 