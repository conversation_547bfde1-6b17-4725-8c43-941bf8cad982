{% extends 'base.html' %}

{% block title %}Manage Interview Recordings{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Interview Recordings</h4>
            {% if can_record_more %}
            <a href="{% url 'interviews:record_response' interview.id %}" class="btn btn-light">
                <i class="fas fa-video me-2"></i>Record New ({{ attempts_left }} left)
            </a>
            {% endif %}
        </div>
        <div class="card-body">
            {% if recordings %}
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        {% for recording in recordings %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 {% if recording == interview.selected_video %}border-primary{% endif %}">
                                <div class="card-body">
                                    <h5 class="card-title">Recording #{{ forloop.counter }}</h5>
                                    <p class="text-muted">{{ recording.recorded_at|date:"F j, Y, g:i a" }}</p>
                                    
                                    <div class="video-preview mb-3">
                                        <video src="{{ recording.video_file.url }}" 
                                               controls 
                                               class="w-100" 
                                               style="border-radius: 8px;">
                                        </video>
                                    </div>

                                    {% if recording.notes %}
                                    <div class="notes-section mb-3">
                                        <h6>Notes:</h6>
                                        <p class="mb-0">{{ recording.notes }}</p>
                                    </div>
                                    {% endif %}

                                    <div class="form-check">
                                        <input type="radio" 
                                               name="selected_recording" 
                                               value="{{ recording.id }}"
                                               class="form-check-input"
                                               {% if recording == interview.selected_video %}checked{% endif %}
                                               onchange="this.form.submit()">
                                        <label class="form-check-label">Select as Best Take</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </form>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h5>No Recordings Yet</h5>
                    <p class="text-muted">Start by recording your first interview attempt.</p>
                    <a href="{% url 'interviews:record_response' interview.id %}" class="btn btn-primary">
                        <i class="fas fa-video me-2"></i>Start Recording
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 