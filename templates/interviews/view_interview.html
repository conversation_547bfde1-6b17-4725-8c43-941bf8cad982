{% extends 'base.html' %}
{% load static %}

{% block title %}Interview Recordings - {{ application.applicant.get_full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Interview Recordings</h4>
                        <a href="{% url 'job_applications' application.job.pk %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Applications
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="applicant-info mb-4">
                        <h5>{{ application.applicant.get_full_name }}</h5>
                        <p class="text-muted mb-0">Applied for: {{ application.job.title }}</p>
                    </div>

                    {% for recording in recordings %}
                    <div class="recording-item mb-4">
                        <h6>Question {{ recording.question_number }}</h6>
                        <p class="mb-2">{{ recording.question.question }}</p>
                        <video src="{{ recording.video_file.url }}" 
                               controls 
                               class="w-100 rounded"
                               preload="metadata">
                        </video>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 