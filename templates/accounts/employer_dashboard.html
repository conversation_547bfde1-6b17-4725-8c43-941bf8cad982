{% extends 'base.html' %}
{% load static %}

{% block title %}Employer Dashboard{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Left Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if user.company_logo %}
                            <img src="{{ user.company_logo.url }}" alt="{{ user.company_name }}" class="img-fluid rounded-circle mb-2" style="width: 100px; height: 100px; object-fit: cover;">
                        {% else %}
                            <div class="company-placeholder rounded-circle mb-2">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        {% endif %}
                        <h5 class="mb-1">{{ user.company_name }}</h5>
                        <p class="text-muted small mb-0">{{ user.email }}</p>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="{% url 'profile_edit' %}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-body">
                    <h6 class="card-title mb-3">Quick Stats</h6>
                    <div class="stats-item mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Active Jobs</span>
                            <span class="badge bg-primary">{{ job_listings.count }}</span>
                        </div>
                    </div>
                    <div class="stats-item mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Total Applications</span>
                            <span class="badge bg-info">{{ total_applications }}</span>
                        </div>
                    </div>
                    <div class="stats-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>New Applications</span>
                            <span class="badge bg-warning">{{ new_applications }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Action Buttons -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">Job Listings</h4>
                <a href="{% url 'jobs:job_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Post New Job
                </a>
            </div>

            <!-- Job Listings -->
            <div class="card shadow-sm">
                <div class="card-body">
                    {% if job_listings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Posted</th>
                                        <th>Applications</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for job in page_obj %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'jobs:job_detail' job.pk %}" class="text-decoration-none">
                                                {{ job.title }}
                                            </a>
                                        </td>
                                        <td>{{ job.created_at|date:"M d, Y" }}</td>
                                        <td>
                                            <a href="{% url 'jobs:job_applications' job.pk %}" class="text-decoration-none">
                                                {{ job.jobapplication_set.count }} applications
                                            </a>
                                        </td>
                                        <td>
                                            {% if job.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'jobs:job_edit' job.pk %}" class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'jobs:job_applications' job.pk %}" class="btn btn-sm btn-outline-info" title="View Applications">
                                                    <i class="fas fa-users"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDelete('{{ job.pk }}')"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                              <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                  <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                      <span aria-hidden="true">&laquo;</span>
                                    </a>
                                  </li>
                                {% else %}
                                  <li class="page-item disabled">
                                    <span class="page-link" aria-label="Previous">&laquo;</span>
                                  </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                  {% if page_obj.number == num %}
                                    <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                  {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                  {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                  <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                      <span aria-hidden="true">&raquo;</span>
                                    </a>
                                  </li>
                                {% else %}
                                  <li class="page-item disabled">
                                    <span class="page-link" aria-label="Next">&raquo;</span>
                                  </li>
                                {% endif %}
                              </ul>
                            </nav>
                        {% endif %}
                    {% else %}          
                        <div class="text-center py-4">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <h5>No Jobs Posted Yet</h5>
                            <p class="text-muted">Start by posting your first job opening.</p>
                            <a href="{% url 'jobs:job_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Post a Job
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Applications -->
            <div class="card stats-card">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">Recent Applications</h5>
                </div>
                <div class="card-body">
                    {% if recent_applications %}
                        {% for application in recent_applications %}
                            <div class="application-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ application.applicant.get_full_name }}</h6>
                                        <p class="text-muted mb-2">Applied for: {{ application.job.title }}</p>
                                    </div>
                                    <span class="badge bg-{{ application.get_status_color }} status-badge">
                                        {{ application.get_status_display }}
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Applied {{ application.applied_at|timesince }} ago</small>
                                    <div class="btn-group">
                                        {% if application.interview_set.exists %}
                                            <a href="{% url 'interviews:view_analysis' application.interview_set.first.id %}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-video me-1"></i>View Interview Analysis
                                            </a>
                                        {% endif %}
                                        <a href="{% url 'jobs:application_detail' application.id %}" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted text-center">No recent applications</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(jobId) {
    if (confirm('Are you sure you want to delete this job posting? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/jobs/${jobId}/delete/`;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.company-placeholder {
    width: 100px;
    height: 100px;
    background-color: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.stats-item {
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.stats-item:last-child {
    border-bottom: none;
}

.table th {
    font-weight: 600;
    color: #4a5568;
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

.list-group-item:hover {
    background-color: #f8fafc;
}

.application-item {
    margin-bottom: 1rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgba(16,30,54,0.06);
    padding: 1.25rem 1.5rem;
    border: none;
    transition: box-shadow 0.2s;
}
.application-item:last-child {
    margin-bottom: 0;
}

.application-item:hover {
    box-shadow: 0 4px 16px 0 rgba(16,30,54,0.10);
}

.application-item h6, .application-item .mb-1 {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.application-item p.text-muted {
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.application-item .status-badge {
    font-size: 0.9rem;
    padding: 0.4em 1.2em;
    border-radius: 20px;
    font-weight: 500;
}

.application-item small.text-muted {
    font-size: 0.98rem;
}
</style>
{% endblock %} 