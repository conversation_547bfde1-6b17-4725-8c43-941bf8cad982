{% extends 'base.html' %}
{% load static %}

{% block title %}Job Seeker Dashboard{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }

    .profile-card {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border: none;
        border-radius: 15px;
    }

    .profile-placeholder {
        width: 120px;
        height: 120px;
        background: linear-gradient(45deg, #4f46e5, #6366f1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        border: 3px solid rgba(255,255,255,0.2);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .profile-placeholder i {
        font-size: 3rem;
        opacity: 0.8;
    }

    .job-search-card {
        background: white;
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }

    .search-form {
        background-color: #f8fafc;
        padding: 1.5rem;
        border-radius: 10px;
    }

    .application-item {
        border: 1px solid #e5e7eb;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .application-item:hover {
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        transform: translateY(-2px);
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
    }

    .form-floating label {
        color: #6b7280;
    }

    .form-floating input:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
    }

    .popular-searches {
        margin-top: 1rem;
    }

    .popular-searches .badge {
        padding: 0.5rem 1rem;
        font-weight: normal;
        transition: all 0.2s;
    }

    .popular-searches .badge:hover {
        background-color: #4f46e5 !important;
        color: white !important;
        text-decoration: none;
    }

    .search-form .btn-primary {
        background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        border: none;
        font-weight: 500;
    }

    .search-form .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
    }

    .profile-image {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .profile-image:hover {
        transform: scale(1.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="card profile-card mb-4">
                <div class="card-body text-center">
                    {% if user.profile_image %}
                        <img src="{{ user.profile_image.url }}" 
                             alt="Profile Picture" 
                             class="profile-image rounded-circle mb-3"
                             style="width: 120px; height: 120px; object-fit: cover; border: 3px solid rgba(255,255,255,0.2);">
                    {% else %}
                        <div class="profile-placeholder">
                            <i class="fas fa-user fa-4x text-white-50"></i>
                        </div>
                    {% endif %}
                    <h5 class="card-title text-white">{{ user.get_full_name }}</h5>
                    <p class="text-white-50">{{ user.email }}</p>
                    {% if user.resume %}
                    <div class="mb-3">
                        <a href="{{ user.resume.url }}" class="btn btn-light btn-sm" download>
                            <i class="fas fa-file-download me-2"></i>Resume
                        </a>
                    </div>
                    {% endif %}
                    <a href="{% url 'profile_edit' %}" class="btn btn-light">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                </div>
            </div>

            <!-- Application Stats -->
            <div class="card stats-card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-4">Application Stats</h5>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>
                            <a href="?filter=all" class="text-decoration-none">Total Applications</a>
                        </span>
                        <span class="badge bg-primary">{{ total_applications }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>
                            <a href="?filter=under_review" class="text-decoration-none">Under Review</a>
                        </span>
                        <span class="badge bg-info">{{ applications_under_review }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <a href="?filter=pending_interview" class="text-decoration-none">Pending Interviews</a>
                        </span>
                        <span class="badge bg-warning">{{ pending_interviews }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-lg-8">
            <!-- Job Search Card -->
            <div class="card job-search-card mb-4">
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-search-location fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Find Your Perfect Job</h5>
                        <p class="text-muted">Search through thousands of job opportunities that match your skills and interests.</p>
                    </div>
                    <form action="{% url 'jobs:job_list' %}" method="get" class="search-form">
                        <div class="row g-3">
                            <div class="col-md-5">
                                <div class="form-floating">
                                    <input type="text" 
                                           class="form-control" 
                                           id="searchKeyword" 
                                           name="q" 
                                           placeholder="Job title, skills, or keywords"
                                           autocomplete="off">
                                    <label for="searchKeyword">
                                        <i class="fas fa-briefcase me-2"></i>What role are you looking for?
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="form-floating">
                                    <input type="text" 
                                           class="form-control" 
                                           id="searchLocation" 
                                           name="location" 
                                           placeholder="City, state, or remote"
                                           autocomplete="off">
                                    <label for="searchLocation">
                                        <i class="fas fa-map-marker-alt me-2"></i>Where?
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary h-100 w-100 d-flex align-items-center justify-content-center">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                        
                        <!-- Advanced Search Filters -->
                        <div class="mt-3">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <select class="form-select" name="employment_type">
                                        <option value="">Employment Type (All)</option>
                                        <option value="full_time">Full Time</option>
                                        <option value="part_time">Part Time</option>
                                        <option value="contract">Contract</option>
                                        <option value="remote">Remote</option>
                                        <option value="internship">Internship</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select" name="experience_level">
                                        <option value="">Experience Level (All)</option>
                                        <option value="entry">Entry Level</option>
                                        <option value="mid">Mid Level</option>
                                        <option value="senior">Senior Level</option>
                                        <option value="executive">Executive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Filters -->
                        <div class="mt-3" >
                            <div class="d-flex flex-wrap gap-4" style="display: flex !important;justify-content: space-evenly;    justify-content: center;">
                                {% comment %} <button type="submit" name="employment_type" value="remote" 
                                        class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-home me-1"></i>Remote Jobs
                                </button> {% endcomment %}
                                <button type="submit" name="employment_type" value="full_time" 
                                        class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-business-time me-1"></i>Full-Time
                                </button>
                                <button type="submit" name="experience_level" value="entry" 
                                        class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-graduation-cap me-1"></i>Entry Level
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Applications -->
            {% if not filter_type %}
            <div class="card stats-card">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">Recent Applications</h5>
                </div>
                <div class="card-body">
                    {% if recent_applications %}
                        {% for application in recent_applications %}
                            <div class="application-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ application.job.title }}</h6>
                                        <p class="text-muted mb-2">{{ application.job.employer.company_name }}</p>
                                    </div>
                                    <span class="badge bg-{{ application.get_status_color }} status-badge">
                                        {{ application.get_status_display }}
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Applied {{ application.applied_at|timesince }} ago</small>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No applications yet.</p>
                            <a href="{% url 'jobs:job_list' %}" class="btn btn-primary">
                                Start Applying
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            {% if filter_type %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>
                            {% if filter_type == 'under_review' %}Applications Under Review{% elif filter_type == 'pending_interview' %}Pending Interviews{% else %}All Applications{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if page_obj %}
                            {% for application in page_obj %}
                                <div class="application-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ application.job.title }}</h6>
                                            <p class="text-muted mb-2">{{ application.job.employer.company_name }}</p>
                                        </div>
                                        <span class="badge bg-{{ application.get_status_color }} status-badge">
                                            {{ application.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">Applied {{ application.applied_at|timesince }} ago</small>
                                        {% if application.interview_set.first and application.interview_set.first.analysis_status == 'pending' %}
                                            <a href="{% url 'interviews:record_response' application.interview_set.first.id %}" class="btn btn-sm btn-primary ms-2">
                                                Record Interview
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}

                            {# Pagination controls #}
                            {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                              <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                  <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if filter_type %}&filter={{ filter_type }}{% endif %}" aria-label="Previous">
                                      <span aria-hidden="true">&laquo;</span>
                                    </a>
                                  </li>
                                {% else %}
                                  <li class="page-item disabled">
                                    <span class="page-link" aria-label="Previous">&laquo;</span>
                                  </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                  {% if page_obj.number == num %}
                                    <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                  {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item"><a class="page-link" href="?page={{ num }}{% if filter_type %}&filter={{ filter_type }}{% endif %}">{{ num }}</a></li>
                                  {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                  <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if filter_type %}&filter={{ filter_type }}{% endif %}" aria-label="Next">
                                      <span aria-hidden="true">&raquo;</span>
                                    </a>
                                  </li>
                                {% else %}
                                  <li class="page-item disabled">
                                    <span class="page-link" aria-label="Next">&raquo;</span>
                                  </li>
                                {% endif %}
                              </ul>
                            </nav>
                            {% endif %}
                        {% else %}
                            <p>No applications found for this filter.</p>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 