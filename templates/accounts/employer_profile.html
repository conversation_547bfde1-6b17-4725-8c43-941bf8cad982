{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Employer Profile{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="form-card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>Edit Company Profile
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}

                        <!-- Company Information -->
                        <div class="form-section">
                            <h5 class="section-title">
                                <i class="fas fa-info-circle me-2"></i>Company Information
                            </h5>
                            
                            <!-- Company Logo -->
                            <div class="mb-4">
                                <label class="form-label">Company Logo</label>
                                <div class="d-flex align-items-center gap-3">
                                    {% if user.company_logo %}
                                        <img src="{{ user.company_logo.url }}" alt="Company Logo" class="rounded" style="width: 100px; height: 100px; object-fit: cover;">
                                    {% endif %}
                                    <div class="flex-grow-1">
                                        {{ form.company_logo|add_class:"form-control" }}
                                        {% if form.company_logo.errors %}
                                            <div class="invalid-feedback d-block">{{ form.company_logo.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Company Name -->
                            <div class="mb-3">
                                <label for="{{ form.company_name.id_for_label }}" class="form-label">Company Name</label>
                                {{ form.company_name|add_class:"form-control form-control-lg" }}
                                {% if form.company_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.company_name.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Company Description -->
                            <div class="mb-3">
                                <label for="{{ form.company_description.id_for_label }}" class="form-label">Company Description</label>
                                {{ form.company_description|add_class:"form-control"|attr:"rows:4" }}
                                {% if form.company_description.errors %}
                                    <div class="invalid-feedback d-block">{{ form.company_description.errors }}</div>
                                {% endif %}
                            </div>

                            <!-- Company Website -->
                            <div class="mb-3">
                                <label for="{{ form.company_website.id_for_label }}" class="form-label">Company Website</label>
                                {{ form.company_website|add_class:"form-control" }}
                                {% if form.company_website.errors %}
                                    <div class="invalid-feedback d-block">{{ form.company_website.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="form-section">
                            <h5 class="section-title">
                                <i class="fas fa-address-card me-2"></i>Contact Information
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                                    {{ form.email|add_class:"form-control" }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback d-block">{{ form.email.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                    {{ form.phone_number|add_class:"form-control" }}
                                    {% if form.phone_number.errors %}
                                        <div class="invalid-feedback d-block">{{ form.phone_number.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="form-section">
                            <h5 class="section-title">
                                <i class="fas fa-map-marker-alt me-2"></i>Location
                            </h5>
                            <div class="mb-3">
                                <label for="{{ form.company_location.id_for_label }}" class="form-label">Company Address</label>
                                {{ form.company_location|add_class:"form-control" }}
                                {% if form.company_location.errors %}
                                    <div class="invalid-feedback d-block">{{ form.company_location.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                            <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
    }
    
    .section-title {
        color: #1a202c;
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e2e8f0;
    }
    
    .form-control-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
</style>
{% endblock %} 