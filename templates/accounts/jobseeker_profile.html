{% extends 'base.html' %}
{% load widget_tweaks %}
{% load static %}

{% block title %}Edit Profile{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>Edit Your Profile
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}

                        <!-- Profile Photo Section -->
                        <div class="mb-4">
                            <h5 class="section-title mb-3">
                                <i class="fas fa-camera me-2"></i>Profile Photo
                            </h5>
                            <div class="d-flex align-items-center gap-3">
                                <div class="profile-upload-preview">
                                    <img id="profilePreview" 
                                         src="{% if user.profile_image %}{{ user.profile_image.url }}{% else %}{% static 'img/default-avatar.png' %}{% endif %}" 
                                         class="rounded-circle" alt="Profile preview">
                                    <div class="upload-overlay">
                                        <i class="fas fa-camera"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="custom-file-upload">
                                        <input type="file" 
                                               name="profile_image" 
                                               id="{{ form.profile_image.id_for_label }}" 
                                               class="d-none"
                                               accept="image/*">
                                        <label for="{{ form.profile_image.id_for_label }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-upload me-2"></i>Change Photo
                                        </label>
                                    </div>
                                    <div class="form-text text-muted">
                                        Upload a professional profile photo (Recommended: Square image, max 5MB)
                                    </div>
                                    {% if form.profile_image.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.profile_image.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="form-section">
                            <h5 class="section-title">
                                <i class="fas fa-user me-2"></i>Personal Information
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                        {{ form.first_name }}
                                        {% if form.first_name.errors %}
                                            <div class="invalid-feedback d-block">{{ form.first_name.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                        {{ form.last_name }}
                                        {% if form.last_name.errors %}
                                            <div class="invalid-feedback d-block">{{ form.last_name.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                        {{ form.email|add_class:"form-control form-control-lg" }}
                                        {% if form.email.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.email.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                        {{ form.phone_number|add_class:"form-control form-control-lg" }}
                                        {% if form.phone_number.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.phone_number.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div class="section mb-4">
                            <h5 class="section-title">
                                <i class="fas fa-briefcase me-2"></i>Professional Information
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.experience_years.id_for_label }}" class="form-label">Years of Experience</label>
                                        {{ form.experience_years|add_class:"form-control form-control-lg" }}
                                        {% if form.experience_years.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.experience_years.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="{{ form.skills.id_for_label }}" class="form-label">Skills</label>
                                        {{ form.skills|add_class:"form-control form-control-lg"|attr:"rows:4" }}
                                        {% if form.skills.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.skills.errors }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text mt-1">
                                            <i class="fas fa-info-circle me-1"></i>Enter your skills separated by commas (e.g., Python, JavaScript, Project Management)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Resume -->
                        <div class="section mb-4">
                            <h5 class="section-title">
                                <i class="fas fa-file-alt me-2"></i>Resume
                            </h5>
                            <div class="resume-section">
                                {% if user.resume %}
                                    <div class="alert alert-info d-flex align-items-center">
                                        <i class="fas fa-file-pdf fa-2x me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Current Resume</h6>
                                            <a href="{{ user.resume.url }}" target="_blank" class="text-decoration-none">
                                                View Resume <i class="fas fa-external-link-alt ms-1"></i>
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="form-group">
                                    <label for="{{ form.resume.id_for_label }}" class="form-label">Upload New Resume</label>
                                    {{ form.resume|add_class:"form-control form-control-lg" }}
                                    <div class="form-text mt-1">
                                        <i class="fas fa-info-circle me-1"></i>Accepted formats: PDF, DOC, DOCX
                                    </div>
                                    {% if form.resume.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.resume.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                            <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Form styling */
    .form-control {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease-in-out;
    }

    .form-control:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
    }

    .form-control-lg {
        padding: 1rem 1.25rem;
    }

    /* Section styling */
    .section {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }

    .section-title {
        color: #1e293b;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e9ecef;
    }

    /* Form group spacing */
    .form-group {
        margin-bottom: 1.25rem;
    }

    .form-label {
        font-weight: 500;
        color: #475569;
        margin-bottom: 0.5rem;
    }

    /* Resume section */
    .resume-section {
        background-color: #f8fafc;
        padding: 1.25rem;
        border-radius: 0.5rem;
    }

    /* Alert styling */
    .alert {
        border: none;
        border-radius: 0.5rem;
    }

    .alert-info {
        background-color: #eff6ff;
        color: #1e40af;
    }

    /* Button styling */
    .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        border-radius: 0.5rem;
        transition: all 0.2s ease-in-out;
    }

    .btn-lg {
        padding: 1rem 2rem;
    }

    .btn-primary {
        background-color: #2563eb;
        border-color: #2563eb;
    }

    .btn-primary:hover {
        background-color: #1d4ed8;
        border-color: #1d4ed8;
    }

    .btn-outline-secondary {
        color: #475569;
        border-color: #cbd5e1;
    }

    .btn-outline-secondary:hover {
        color: #1e293b;
        background-color: #f1f5f9;
        border-color: #94a3b8;
    }

    /* Invalid feedback styling */
    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Form text styling */
    .form-text {
        color: #64748b;
        font-size: 0.875rem;
    }

    /* Profile image upload styles */
    .profile-upload-preview {
        position: relative;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid #e9ecef;
        background-color: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .profile-upload-preview:hover {
        border-color: #0d6efd;
        box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
    }

    .profile-upload-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .profile-upload-preview .upload-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0));
        color: white;
        padding: 15px 0 8px;
        font-size: 14px;
        text-align: center;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .profile-upload-preview:hover .upload-overlay {
        opacity: 1;
    }

    .custom-file-upload {
        position: relative;
    }

    .custom-file-upload input[type="file"] {
        position: absolute;
        width: 1px;
        height: 1px;
        opacity: 0;
    }

    .section-title {
        color: #1a1a1a;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewImage(input) {
    const preview = document.getElementById('profilePreview');
    const file = input.files[0];
    
    if (file) {
        // Check file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file');
            input.value = '';
            return;
        }
        
        // Check file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Please select an image less than 5MB');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
        }
        reader.readAsDataURL(file);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // File input and preview handling
    const fileInput = document.querySelector('input[type="file"]');
    const preview = document.querySelector('.profile-upload-preview');
    
    if (fileInput && preview) {
        fileInput.addEventListener('change', function() {
            previewImage(this);
        });

        preview.addEventListener('click', function() {
            fileInput.click();
        });
    }
});
</script>
{% endblock %} 