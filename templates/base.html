{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}Job Portal{% endblock %}</title>
  
  <!-- External CSS -->
  <!--
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"> 
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  -->
  <!-- Custom CSS -->
  <!--
  <link href="{% static 'css/base.css' %}" rel="stylesheet">
  <link href="{% static 'css/forms.css' %}" rel="stylesheet">
  -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <link href="{% static 'css/style.css' %}" rel="stylesheet">
  
  {% block extra_css %}{% endblock %}
  <link rel="icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">
  <link rel="shortcut icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">
</head>
<body>
  <header class="site-header">
    <div class="site-container">
      <div class="header-inner">
        <!-- Logo -->
        <a class="header-logo-holder" href="{% url 'home' %}">
          <img
            class="header-logo-img" 
            src="../static/images/logo-vouch.svg" 
            alt="Logo of vouch"
          >
          <span class="header-logo-text">Vouch</span>
        </a>
        <!-- Site Navigation -->
        <div class="header-right">
          <nav class="site-navigation">
            <ul>
              <li><a href="{% url 'home' %}">Home</a></li>
              <li><a href="#">Find Candidates</a></li>
              <li><a href="#">Browse Companies</a></li>
              <li><a href="#">Contact</a></li>
              <li><a href="#">AboutUs</a></li>
            </ul>  
          </nav>
          <div class="header-button">
            <a href="{% url 'login' %}" class="btn btn--primary">Login</a>
            <a href="{% url 'signup' %}" class="btn btn--secondary">Signup</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
   {% block content %}{% endblock %}
    <div class="main-content">
        <div class="container py-4">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            
        </div>
    </div>

    <footer class="site-footer">
      <div class="footer-top">
        <div class="site-container">
          <div class="site-footer__inner">
            <div class="site-footer__col"></div>
            <div class="site-footer__col"></div>
            <div class="site-footer__col"></div>
            <div class="site-footer__col"></div>
            <div class="site-footer__col"></div>
          </div>
        </div>
      </div>
      <div class="site-container">

      </div>
    </footer>

    <!-- Footer 
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-primary mb-3">Job Portal</h5>
                    <p class="text-muted">Connecting talented professionals with great opportunities.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">&copy; 2024 Job Portal. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>
    -->
  
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  
  <script>
    // Prevent browser caching issues with authentication state
    window.addEventListener("pageshow", function(event) {
      if (event.persisted || (window.performance && window.performance.navigation.type === 2)) {
        // Page was loaded from cache, reload to get fresh authentication state
        window.location.reload();
      }
    });
  </script>
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="{% static 'js/main.js' %}"></script>
  {% block extra_js %}{% endblock %}
</body>
</html> 