{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}{% if is_edit %}Edit{% else %}Post New{% endif %} Job{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-9">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas {% if is_edit %}fa-edit{% else %}fa-plus-circle{% endif %} me-2"></i>
                        {% if is_edit %}Edit{% else %}Post New{% endif %} Job
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for message in messages %}
                                <li>{{ message }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {% for error in form.non_field_errors %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <div>{{ error }}</div>
                        </div>
                        {% endfor %}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endif %}

                    <form method="post" class="job-form">
                        {% csrf_token %}
                        <!-- Basic Information -->
                        <div class="section mb-4" data-section="1">
                            <h5 class="section-title">
                                <span class="section-number">1</span>
                                <i class="fas fa-briefcase me-2"></i>
                                Basic Job Details
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12 form-group">
                                            <label for="id_title" class="form-label required">Job Title</label>
                                            {{ form.title }}
                                            {% if form.title.errors %}
                                            <div class="invalid-feedback d-block error-message">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.title.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                            {% if form.title.help_text %}
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                {{ form.title.help_text }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="id_category" class="form-label required">Category</label>
                                            {{ form.category }}
                                            {% if form.category.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.category.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                            {% if form.category.help_text %}
                                            <small class="form-text text-muted">{{ form.category.help_text }}</small>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="id_employment_type" class="form-label required">Employment Type</label>
                                            {{ form.employment_type }}
                                            {% if form.employment_type.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.employment_type.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                            {% if form.employment_type.help_text %}
                                            <small class="form-text text-muted">{{ form.employment_type.help_text }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Location & Compensation -->
                        <div class="section mb-4" data-section="2">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">2</span>
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Location & Compensation
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="id_location" class="form-label required">Location</label>
                                            {{ form.location }}
                                            {% if form.location.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.location.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="id_salary_range" class="form-label required">Salary Range</label>
                                            {{ form.salary_range }}
                                            {% if form.salary_range.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.salary_range.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                            {% if form.salary_range.help_text %}
                                            <small class="form-text text-muted">{{ form.salary_range.help_text }}</small>
                                            {% endif %}
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                {{ form.remote_work }}
                                                <label class="form-check-label" for="id_remote_work">
                                                    Remote Work Available
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Deadline and Experience -->
                        <div class="section mb-4" data-section="3">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">3</span>
                                <i class="fas fa-calendar me-2"></i>
                                Deadline & Experience
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="id_deadline" class="form-label required">Application Deadline</label>
                                            {{ form.deadline }}
                                            {% if form.deadline.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.deadline.errors|join:", " }}
                                            </div>
                                            {% endif %}
                                            {% if form.deadline.help_text %}
                                            <small class="form-text text-muted">{{ form.deadline.help_text }}</small>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="id_experience_level" class="form-label required">Experience Level</label>
                                            {{ form.experience_level }}
                                            {% if form.experience_level.errors %}
                                                <div class="invalid-feedback d-block">{{ form.experience_level.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Job Description -->
                        <div class="section mb-4" data-section="4">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">4</span>
                                <i class="fas fa-file-alt me-2"></i>
                                Job Description
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            {% render_field form.description class="form-control" rows="6" placeholder="Provide a detailed description of the role, responsibilities, and what candidates can expect..." %}
                                            <small class="text-muted">Provide a comprehensive description of the role, responsibilities, and what candidates can expect.</small>
                                            {% if form.description.errors %}
                                                <div class="invalid-feedback d-block">{{ form.description.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Job Requirements -->
                        <div class="section mb-4" data-section="5">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">5</span>
                                <i class="fas fa-list-check me-2"></i>
                                Job Requirements
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            {% render_field form.requirements class="form-control" rows="5" placeholder="List the key requirements, qualifications, and skills needed for this position..." %}
                                            <small class="text-muted">List all required qualifications, skills, and experience.</small>
                                            {% if form.requirements.errors %}
                                                <div class="invalid-feedback d-block">{{ form.requirements.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Interview Settings -->
                        <div class="section mb-4" data-section="6">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">6</span>
                                <i class="fas fa-video me-2"></i>
                                Video Interview Settings
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <!-- <div class="col-md-6"> -->
                                            <div hidden class="custom-toggle-container">
                                                <input hidden type="checkbox" id="id_video_interview_required" name="video_interview_required" class="custom-toggle-input" checked>
                                                <label class="custom-toggle-label" for="id_video_interview_required">
                                                    <span class="toggle-track"></span>
                                                    <span class="toggle-text">
                                                        <strong>Video Interview</strong>
                                                        <small class="text-muted d-block">Toggle to enable/disable video interviews</small>
                                                    </span>
                                                </label>
                                            </div>
                                        <!-- </div> -->
                                        <div class="col-md-6">
                                            <label for="id_interview_duration" class="form-label">Interview Duration (minutes)</label>
                                            {% render_field form.interview_duration class="form-control" type="number" min="1" max="60" %}
                                            <small class="text-muted d-block">Set time limit for each question (1-60 minutes)</small>
                                            {% if form.interview_duration.errors %}
                                                <div class="invalid-feedback d-block">{{ form.interview_duration.errors|join:", " }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Interview Questions Section -->
                        <div class="section mb-4" id="interview-questions-section" data-section="7">
                            <h5 class="section-title d-flex align-items-center">
                                <span class="section-number">7</span>
                                <i class="fas fa-question-circle me-2"></i>
                                Interview Questions
                                <small class="text-muted">(At least one required)</small>
                            </h5>
                            <div class="card section-card">
                                <div class="card-body">
                                    {{ question_formset.management_form }}
                                    <div id="question-formset">
                                        {% for question_form in question_formset %}
                                        <div class="question-form mb-3">
                                            {{ question_form.id }}
                                            <div class="input-group">
                                                {% render_field question_form.question class="form-control" placeholder="Enter interview question..." %}
                                                {% if forloop.counter > 1 %}
                                                <button type="button" class="btn btn-outline-danger remove-question">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                            {% if question_form.question.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ question_form.question.errors|join:", " }}
                                                </div>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    <button type="button" class="btn btn-outline-primary" id="add-question">
                                        <i class="fas fa-plus me-2"></i>Add Question
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'dashboard' %}" class="btn btn-light">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas {% if is_edit %}fa-save{% else %}fa-paper-plane{% endif %} me-2"></i>
                                {% if is_edit %}Save Changes{% else %}Post Job{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Section Styling */
    .section {
        position: relative;
        transition: all 0.3s ease;
    }

    .section-number {
        width: 28px;
        height: 28px;
        background: #4f46e5;
        color: white;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-weight: 600;
    }

    .section-title {
        color: #1f2937;
        font-size: 1.1rem;
        margin-bottom: 1rem;
        padding: 8px;
        border-radius: 6px;
        background: #f3f4f6;
    }

    .section-card {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .section-card:hover {
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    /* Form Controls */
    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 6px;
        padding: 0.625rem 1rem;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    /* Error States */
    .error-message {
        background: #fee2e2;
        border-left: 4px solid #dc2626;
        color: #991b1b;
        padding: 0.75rem 1rem;
        margin-top: 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
    }

    .form-control.is-invalid {
        border-color: #dc2626;
        background-image: none;
    }

    .form-control.is-invalid:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    /* Help Text */
    .form-text {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
    }

    /* Required Field */
    .form-label.required {
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .form-label.required::after {
        content: "*";
        color: #dc2626;
        font-weight: bold;
    }

    /* Animations */
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section {
        animation: slideIn 0.3s ease-out;
    }

    /* Success States */
    .field-success .form-control {
        border-color: #059669;
    }

    .field-success .form-label::before {
        content: "✓";
        color: #059669;
        margin-right: 4px;
    }

    .form-label {
        font-weight: 500;
        color: #4b5563;
    }

    .form-label.required::after {
        content: "*";
        color: #dc3545;
        margin-left: 4px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    }

    textarea {
        resize: vertical;
    }

    .form-check-input:checked {
        background-color: #4f46e5;
        border-color: #4f46e5;
    }

    .question-form {
        position: relative;
    }

    .remove-question {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
    }

    /* Custom Toggle Switch */
    .custom-toggle-container {
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }

    .custom-toggle-input {
        display: none;
    }

    .custom-toggle-label {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        cursor: pointer;
        margin: 0;
    }

    .toggle-track {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 26px;
        background-color: #e5e7eb;
        border-radius: 13px;
        transition: all 0.3s ease;
    }

    .toggle-track::after {
        content: '';
        position: absolute;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background-color: white;
        top: 2px;
        left: 2px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .custom-toggle-input:checked + .custom-toggle-label .toggle-track {
        background-color: #4f46e5;
    }

    .custom-toggle-input:checked + .custom-toggle-label .toggle-track::after {
        transform: translateX(24px);
    }

    .toggle-text {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    /* Question Section Transitions */
    #interview-questions-section {
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        transition: all 0.3s ease-in-out;
    }

    #interview-questions-section.visible {
        max-height: 2000px;
        opacity: 1;
        margin-bottom: 1.5rem;
    }

    /* Question Form Animations */
    .question-form {
        transform-origin: top;
        transition: all 0.3s ease;
    }

    .question-form.new-question {
        animation: slideDown 0.3s ease forwards;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .question-form.removing {
        animation: slideUp 0.3s ease forwards;
    }

    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-10px);
        }
    }

    /* Enhanced Error Styling */
    .invalid-feedback {
        display: block !important;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding: 0.375rem 0.75rem;
        background-color: rgba(220, 53, 69, 0.1);
        border-radius: 0.25rem;
        border-left: 3px solid #dc3545;
    }

    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .form-control.is-invalid:focus,
    .form-select.is-invalid:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }

    /* Required field indicator */
    .form-label.required::after {
        content: "*";
        color: #dc3545;
        margin-left: 4px;
    }

    /* Help text styling */
    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Error icon animation */
    .invalid-feedback i {
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-2px); }
        75% { transform: translateX(2px); }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.job-form');
    const progressBar = document.querySelector('.progress-bar');
    const sections = document.querySelectorAll('.section');
    const badges = document.querySelectorAll('.badge');

    // Update progress as user fills form
    function updateProgress() {
        const totalFields = form.querySelectorAll('input, select, textarea').length;
        const filledFields = form.querySelectorAll('input:valid, select:valid, textarea:valid').length;
        const progress = (filledFields / totalFields) * 100;
        progressBar.style.width = `${progress}%`;
        
        // Update section badges
        sections.forEach((section, index) => {
            const sectionFields = section.querySelectorAll('input, select, textarea');
            const filledSectionFields = section.querySelectorAll('input:valid, select:valid, textarea:valid');
            
            if (filledSectionFields.length === sectionFields.length) {
                badges[index].classList.remove('bg-secondary');
                badges[index].classList.add('bg-success');
            }
        });
    }

    // Add real-time validation
    form.querySelectorAll('input, select, textarea').forEach(field => {
        field.addEventListener('change', function() {
            if (this.checkValidity()) {
                this.classList.add('is-valid');
                this.closest('.form-group').classList.add('field-success');
            }
            updateProgress();
        });
    });

    // Smooth scroll to error
    if (document.querySelector('.invalid-feedback')) {
        document.querySelector('.invalid-feedback').scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }

    const videoInterviewCheckbox = document.querySelector('#id_video_interview_required');
    const questionsSection = document.querySelector('#interview-questions-section');
    const addQuestionBtn = document.querySelector('#add-question');
    const questionFormset = document.querySelector('#question-formset');
    const totalFormsInput = document.querySelector('[name="interview_questions-TOTAL_FORMS"]');
    
    // Set initial state
    questionsSection.classList.add('visible');
    
    // Toggle questions section with animation
    videoInterviewCheckbox.addEventListener('change', function() {
        if (this.checked) {
            questionsSection.classList.add('visible');
        } else {
            const confirmed = confirm('Disabling video interviews will remove all interview questions. Are you sure?');
            if (confirmed) {
                questionsSection.classList.remove('visible');
            } else {
                this.checked = true;
            }
        }
    });

    // Add new question form with animation
    addQuestionBtn.addEventListener('click', function() {
        const forms = questionFormset.getElementsByClassName('question-form');
        const formCount = forms.length;
        
        if (formCount >= 5) {
            alert('Maximum 5 questions allowed');
            return;
        }
        
        const newForm = forms[0].cloneNode(true);
        newForm.classList.add('new-question');
        
        // Clear values and update index
        newForm.querySelectorAll('input, textarea').forEach(input => {
            if (input.type !== 'hidden') {
                input.value = '';
            }
        });

        // Update form indices
        newForm.innerHTML = newForm.innerHTML.replace(/interview_questions-\d+-/g, `interview_questions-${formCount}-`);
        
        // Add remove button
        if (!newForm.querySelector('.remove-question')) {
            const inputGroup = newForm.querySelector('.input-group');
            inputGroup.insertAdjacentHTML('beforeend', `
                <button type="button" class="btn btn-outline-danger remove-question">
                    <i class="fas fa-times"></i>
                </button>
            `);
        }
        
        questionFormset.appendChild(newForm);
        totalFormsInput.value = formCount + 1;
        
        // Animate new form
        setTimeout(() => {
            newForm.classList.remove('new-question');
        }, 10);
    });
    
    // Remove question form with animation
    questionFormset.addEventListener('click', function(e) {
        const removeBtn = e.target.closest('.remove-question');
        if (removeBtn) {
            const form = removeBtn.closest('.question-form');
            const forms = questionFormset.getElementsByClassName('question-form');
            
            if (forms.length > 1) {
                form.classList.add('removing');
                
                setTimeout(() => {
                    form.remove();
                    totalFormsInput.value = forms.length - 1;
                    
                    // Re-number remaining forms
                    const remainingForms = questionFormset.querySelectorAll('.question-form');
                    remainingForms.forEach((form, index) => {
                        form.querySelectorAll('[name*="interview_questions-"]').forEach(input => {
                            input.name = input.name.replace(/interview_questions-\d+-/, `interview_questions-${index}-`);
                        });
                    });
                }, 300);
            }
        }
    });
});
</script>
{% endblock %} 