{% extends 'base.html' %}
{% load static %}

{% block title %}Application Details{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white py-3">
            <h4 class="mb-0">Application Details</h4>
        </div>
        <div class="card-body">
            <!-- Applicant Information -->
            <div class="row mb-4">
                <div class="col-md-3 text-center">
                    <!-- Profile Image -->
                    <div class="mb-3">
                        {% if application.applicant.profile_image %}
                            <img src="{{ application.applicant.profile_image.url }}" 
                                 alt="Profile Picture" 
                                 class="rounded-circle img-thumbnail"
                                 style="width: 150px; height: 150px; object-fit: cover;">
                        {% else %}
                            <div class="profile-placeholder rounded-circle mx-auto"
                                 style="width: 150px; height: 150px; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-9">
                    <h5 class="mb-3">{{ application.applicant.get_full_name }}</h5>
                    <p class="mb-2">
                        <i class="fas fa-envelope me-2"></i>{{ application.applicant.email }}
                    </p>
                    {% if application.applicant.phone_number %}
                    <p class="mb-2">
                        <i class="fas fa-phone me-2"></i>{{ application.applicant.phone_number }}
                    </p>
                    {% endif %}
                    
                    <!-- Resume Download -->
                    {% if application.applicant.resume %}
                    <div class="mt-3">
                        <a href="{{ application.applicant.resume.url }}" 
                           class="btn btn-outline-primary" 
                           download>
                            <i class="fas fa-file-download me-2"></i>Download Resume
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <hr>

            <!-- Application Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6 class="text-muted mb-2">Application Status</h6>
                    <span class="badge bg-{{ application.get_status_color }} px-3 py-2">
                        {{ application.get_status_display }}
                    </span>
                </div>
                <div class="col-md-6">
                    <h6 class="text-muted mb-2">Applied On</h6>
                    <p>{{ application.applied_at|date:"F j, Y" }}</p>
                </div>
            </div>

            <!-- Cover Letter -->
            {% if application.cover_letter %}
            <div class="mb-4">
                <h6 class="text-muted mb-2">Cover Letter</h6>
                <div class="p-3 bg-light rounded">
                    {{ application.cover_letter|linebreaks }}
                </div>
            </div>
            {% endif %}

            <!-- Interview Score -->
            {% if application.interview_set.exists %}
            <div class="mb-4">
                <h6 class="text-muted mb-2">Interview Score</h6>
                <div class="d-flex align-items-center">
                    <div class="h4 mb-0 {% if application.interview_set.first.ai_score >= 7 %}text-success
                                       {% elif application.interview_set.first.ai_score >= 5 %}text-warning
                                       {% else %}text-danger{% endif %}">
                        {{ application.interview_set.first.ai_score|floatformat:1 }}/10
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Interview Analysis Section -->
            {% if application.job.video_interview_required %}
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Video Interview Questions</h6>
                    <div class="card bg-light">
                        <div class="card-body">
                            {% if application.job.interview_questions.exists %}
                                <ol class="mb-3">
                                    {% for question in application.job.interview_questions.all %}
                                        <li class="mb-2">{{ question.question }}</li>
                                    {% endfor %}
                                </ol>
                                
                                {% if application.interview_set.exists %}
                                    {% with interview=application.interview_set.first %}
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-1"><strong>Interview Status:</strong> 
                                                    <span class="badge bg-{{ interview.get_status_color }}">
                                                        {{ interview.get_analysis_status_display }}
                                                    </span>
                                                </p>
                                                <p class="mb-0"><small class="text-muted">
                                                    Completed {{ interview.created_at|timesince }} ago
                                                </small></p>
                                            </div>
                                            <a href="{% url 'interviews:view_analysis' interview.id %}" 
                                               class="btn btn-primary">
                                                <i class="fas fa-video me-2"></i>View Analysis
                                            </a>
                                        </div>
                                    {% endwith %}
                                {% else %}
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Video interview pending
                                    </div>
                                {% endif %}
                            {% else %}
                                <div class="alert alert-warning mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No interview questions have been set up for this job.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="mt-4">
                <a href="{% url 'jobs:job_applications' application.job.id %}" 
                   class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Applications
                </a>
                {% if user.is_employer %}
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-edit me-2"></i>Update Status
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        {% for status_code, status_label in status_choices %}
                        <li>
                            <form method="post" action="{% url 'jobs:update_application_status' application.id %}" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="status" value="{{ status_code }}">
                                <button type="submit" class="dropdown-item {% if status_code == application.status %}active{% endif %}">
                                    <i class="fas fa-circle me-2 {% if status_code == application.status %}text-success{% else %}text-muted{% endif %}" 
                                       style="font-size: 0.5em; vertical-align: middle;"></i>
                                    {{ status_label }}
                                </button>
                            </form>
                        </li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
.profile-placeholder {
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.img-thumbnail {
    padding: 0.5rem;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.img-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
{% endblock %} 