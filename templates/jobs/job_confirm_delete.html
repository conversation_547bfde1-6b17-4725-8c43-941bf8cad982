{% extends 'base.html' %}

{% block title %}Delete Job - {{ job.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-4">Are you sure you want to delete the job posting "<strong>{{ job.title }}</strong>"?</p>
                    <p class="text-danger mb-4">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        This action cannot be undone. All associated applications will also be deleted.
                    </p>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Delete Job
                            </button>
                            <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 