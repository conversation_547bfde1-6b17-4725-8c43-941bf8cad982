{% extends 'base.html' %}
{% load static %}

{% block title %}{{ job.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ job.title }}</h4>
            {% if user.is_employer and user == job.employer %}
                <div class="btn-group">
                    <a href="{% url 'jobs:job_edit' job.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'jobs:job_applications' job.pk %}" class="btn btn-outline-info">
                        <i class="fas fa-users"></i> Applications
                    </a>
                </div>
            {% endif %}
        </div>
        <div class="card-body">
            <!-- Job Details -->
            <div class="row">
                <div class="col-md-8">
                    <h5>Job Description</h5>
                    <p>{{ job.description|linebreaks }}</p>
                    
                    <h5 class="mt-4">Requirements</h5>
                    <p>{{ job.requirements|linebreaks }}</p>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>Job Details</h6>
                            <ul class="list-unstyled">
                                <li><strong>Company:</strong> {{ job.employer.company_name }}</li>
                                <li><strong>Location:</strong> {{ job.location }}</li>
                                <li><strong>Posted:</strong> {{ job.created_at|date:"F j, Y" }}</li>
                                <li><strong>Applications:</strong> {{ job.jobapplication_set.count }}</li>
                                <li><strong>Status:</strong> 
                                    <span class="badge bg-{{ job.is_active|yesno:'success,secondary' }}">
                                        {{ job.is_active|yesno:'Active,Inactive' }}
                                    </span>
                                </li>
                            </ul>
                            
                            {% if user.is_jobseeker %}
                                {% if has_applied %}
                                    <button class="btn btn-secondary w-100" disabled>Already Applied</button>
                                {% else %}
                                    <a href="{% url 'jobs:job_apply' job.pk %}" class="btn btn-primary w-100">Apply Now</a>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 