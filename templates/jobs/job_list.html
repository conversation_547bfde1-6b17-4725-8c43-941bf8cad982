{% extends 'base.html' %}
{% load static %}
{% load job_tags %}

{% block title %}Available Jobs{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Search Section -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="get" class="search-form">
                <!-- Main Search Fields -->
                <div class="row g-3 mb-3">
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text" 
                                   class="form-control" 
                                   id="searchKeyword" 
                                   name="q" 
                                   value="{{ search_query }}"
                                   placeholder="Job title, skills, or keywords">
                            <label for="searchKeyword">
                                <i class="fas fa-briefcase me-2"></i>What role are you looking for?
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text" 
                                   class="form-control" 
                                   id="searchLocation" 
                                   name="location" 
                                   value="{{ location_query }}"
                                   placeholder="City, state, or remote">
                            <label for="searchLocation">
                                <i class="fas fa-map-marker-alt me-2"></i>Where?
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary h-100 w-100">
                            <i class="fas fa-search me-2"></i>Search Jobs
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="row g-3">
                    <div class="col-md-4">
                        <select class="form-select" name="employment_type">
                            <option value="">Employment Type (All)</option>
                            <option value="full_time" {% if employment_type == 'full_time' %}selected{% endif %}>Full Time</option>
                            <option value="part_time" {% if employment_type == 'part_time' %}selected{% endif %}>Part Time</option>
                            <option value="contract" {% if employment_type == 'contract' %}selected{% endif %}>Contract</option>
                            <!-- <option value="remote" {% if employment_type == 'remote' %}selected{% endif %}>Remote</option> -->
                            <option value="internship" {% if employment_type == 'internship' %}selected{% endif %}>Internship</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="experience_level">
                            <option value="">Experience Level (All)</option>
                            <option value="entry" {% if experience_level == 'entry' %}selected{% endif %}>Entry Level</option>
                            <option value="mid" {% if experience_level == 'mid' %}selected{% endif %}>Mid Level</option>
                            <option value="senior" {% if experience_level == 'senior' %}selected{% endif %}>Senior Level</option>
                            <option value="executive" {% if experience_level == 'executive' %}selected{% endif %}>Executive</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="sort">
                            <option value="-created_at" {% if sort == '-created_at' %}selected{% endif %}>Newest First</option>
                            <option value="created_at" {% if sort == 'created_at' %}selected{% endif %}>Oldest First</option>
                        </select>
                    </div>
                </div>

                <!-- Quick Filters -->
                <div class="mt-3">
                    <div class="d-flex flex-wrap gap-2">
                        <button type="submit" 
                                name="remote" 
                                value="remote" 
                                class="btn {% if remote == 'remote' %}btn-primary{% else %}btn-outline-primary{% endif %} btn-sm">
                            <i class="fas fa-home me-1"></i>Remote Jobs
                        </button>
                        <button type="submit" 
                                name="employment_type" 
                                value="full_time" 
                                class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-business-time me-1"></i>Full-Time
                        </button>
                        <button type="submit" 
                                name="experience_level" 
                                value="entry" 
                                class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-graduation-cap me-1"></i>Entry Level
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Active Filters Section -->
    {% if search_query or location_query or employment_type or experience_level or remote %}
    <div class="active-filters mb-4">
        <div class="d-flex align-items-center gap-2 flex-wrap">
            <span class="text-muted me-2">Active Filters:</span>
            {% if search_query %}
            <div class="filter-tag">
                <span class="badge bg-primary">
                    <i class="fas fa-search me-1"></i>{{ search_query }}
                    <a href="?{% query_transform request.GET 'q' %}" class="text-white ms-2 text-decoration-none">×</a>
                </span>
            </div>
            {% endif %}
            
            {% if location_query %}
            <div class="filter-tag">
                <span class="badge bg-primary">
                    <i class="fas fa-map-marker-alt me-1"></i>{{ location_query }}
                    <a href="?{% query_transform request.GET 'location' %}" class="text-white ms-2 text-decoration-none">×</a>
                </span>
            </div>
            {% endif %}
            
            {% if remote == 'remote' %}
            <div class="filter-tag">
                <span class="badge bg-primary">
                    <i class="fas fa-home me-1"></i>Remote Jobs
                    <a href="?{% query_transform request.GET 'remote' %}" class="text-white ms-2 text-decoration-none">×</a>
                </span>
            </div>
            {% endif %}
            
            {% if employment_type %}
            <div class="filter-tag">
                <span class="badge bg-primary">
                    <i class="fas fa-briefcase me-1"></i>{{ employment_type|replace:"_"|title }}
                    <a href="?{% query_transform request.GET 'employment_type' %}" class="text-white ms-2 text-decoration-none">×</a>
                </span>
            </div>
            {% endif %}
            
            {% if experience_level %}
            <div class="filter-tag">
                <span class="badge bg-primary">
                    <i class="fas fa-layer-group me-1"></i>{{ experience_level|title }} Level
                    <a href="?{% query_transform request.GET 'experience_level' %}" class="text-white ms-2 text-decoration-none">×</a>
                </span>
            </div>
            {% endif %}
            
            <a href="{% url 'jobs:job_list' %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-times me-1"></i>Clear All
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Results Section -->
    <div class="row">
        <!-- Results Count -->
        <div class="col-12 mb-3">
            <h5>{{ total_jobs }} Jobs Found {% if search_query or location_query %}for "{{ search_query }}" {% if location_query %}in {{ location_query }}{% endif %}{% endif %}</h5>
        </div>

        <!-- Job Listings -->
        {% if jobs %}
            {% for job in jobs %}
                <div class="col-md-6 mb-4">
                    <div class="card h-100 shadow-sm hover-shadow">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="card-title mb-1">
                                        <a href="{% url 'jobs:job_detail' job.pk %}" class="text-decoration-none">
                                            {{ job.title }}
                                        </a>
                                    </h5>
                                    <h6 class="text-muted mb-2">{{ job.employer.company_name }}</h6>
                                </div>
                                <span class="badge bg-primary">{{ job.get_employment_type_display }}</span>
                            </div>
                            
                            <div class="mb-3">
                                <p class="mb-1">
                                    <i class="fas fa-map-marker-alt me-2"></i>{{ job.location }}
                                    {% if job.remote_work %}<span class="badge bg-success ms-2">Remote</span>{% endif %}
                                </p>
                                <p class="mb-1"><i class="fas fa-money-bill-wave me-2"></i>{{ job.salary_range }}</p>
                                <p class="mb-0">
                                    <i class="fas fa-clock me-2"></i>Posted {{ job.created_at|timesince }} ago
                                </p>
                            </div>

                            <div class="d-grid">
                                <a href="{% url 'jobs:job_detail' job.pk %}" class="btn btn-outline-primary">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No jobs found</h5>
                    <p class="text-muted">Try adjusting your search criteria</p>
                </div>
            </div>
        {% endif %}
    </div>
</div>

{% block extra_css %}
<style>
    .search-form {
        background-color: #f8fafc;
        padding: 1.5rem;
        border-radius: 10px;
    }

    .form-floating label {
        color: #6b7280;
    }

    .form-floating input:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
    }

    .hover-shadow:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
    }

    .active-filters {
        background-color: #f8fafc;
        padding: 1rem;
        border-radius: 10px;
    }

    .filter-tag .badge {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    .filter-tag a:hover {
        opacity: 0.8;
    }
</style>
{% endblock %}
{% endblock %} 