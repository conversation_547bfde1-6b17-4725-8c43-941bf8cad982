{% extends 'base.html' %}

{% block title %}Apply for {{ job.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">Apply for: {{ job.title }}</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% for field in form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                {{ field }}
                                {% if field.help_text %}
                                    <div class="form-text">{{ field.help_text }}</div>
                                {% endif %}
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ field.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        {% if has_interview %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                This position requires a video interview. After submitting your application, 
                                you will be directed to complete the interview process.
                            </div>
                            
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5 class="mb-0">Interview Questions</h5>
                                </div>
                                <div class="card-body">
                                    <ol>
                                        {% for question in job.interview_questions.all %}
                                            <li class="mb-2">{{ question.question }}</li>
                                        {% endfor %}
                                    </ol>
                                </div>
                            </div>
                        {% endif %}

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">Submit Application</button>
                            <a href="{% url 'jobs:job_detail' job.pk %}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Job Details</h5>
                            <ul class="list-unstyled">
                                <li><strong>Company:</strong> {{ job.employer.company_name }}</li>
                                <li><strong>Location:</strong> {{ job.location }}</li>
                                <li><strong>Type:</strong> {{ job.get_employment_type_display }}</li>
                                <li><strong>Posted:</strong> {{ job.created_at|date:"F j, Y" }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 