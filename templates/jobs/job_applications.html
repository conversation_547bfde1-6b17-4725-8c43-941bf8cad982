{% extends 'base.html' %}
{% load static %}

{% block title %}Applications for {{ job.title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
<style>
    /* Make modal wider */
    .modal-lg {
        max-width: 1000px !important;
    }

    .markdown-body {
        box-sizing: border-box;
        min-width: 200px;
        margin: 0 auto;
        padding: 20px;
        font-size: 16px !important;
        line-height: 1.6 !important;
        color: #24292e !important;
    }

    .analysis-content {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 20px;
    }

    /* Headings */
    .markdown-body h1,
    .markdown-body h2,
    .markdown-body h3 {
        color: #1a202c !important;
        font-weight: 600 !important;
        margin-top: 24px !important;
        margin-bottom: 16px !important;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 8px;
    }

    /* Lists */
    .markdown-body ul,
    .markdown-body ol {
        padding-left: 2em !important;
        margin-bottom: 16px !important;
    }

    .markdown-body li {
        margin: 8px 0 !important;
    }

    /* Sections */
    .analysis-section {
        background-color: #f8fafc;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 24px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* Strengths and Weaknesses */
    .strength-item {
        color: #047857 !important;
        font-weight: 500;
        padding: 8px 12px;
        background-color: #ecfdf5;
        border-radius: 4px;
        margin-bottom: 8px;
        border-left: 4px solid #059669;
    }

    .weakness-item {
        color: #991b1b !important;
        font-weight: 500;
        padding: 8px 12px;
        background-color: #fef2f2;
        border-radius: 4px;
        margin-bottom: 8px;
        border-left: 4px solid #dc2626;
    }

    /* Bold and emphasis */
    .markdown-body strong {
        color: #1a202c !important;
        font-weight: 600 !important;
    }

    .markdown-body em {
        color: #4a5568 !important;
    }

    /* Evaluation criteria sections */
    .markdown-body h2 + p {
        color: #4a5568 !important;
        font-size: 15px !important;
        line-height: 1.6 !important;
        margin-bottom: 16px !important;
    }

    /* Quotes */
    .markdown-body blockquote {
        border-left: 4px solid #cbd5e0 !important;
        color: #4a5568 !important;
        padding: 0 16px !important;
        margin: 16px 0 !important;
    }

    /* Code blocks if any */
    .markdown-body code {
        background-color: #f7fafc !important;
        border-radius: 4px !important;
        padding: 2px 6px !important;
        font-size: 14px !important;
        color: #2d3748 !important;
    }

    .gap-3 {
        gap: 1rem;
    }
    .form-select {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }
    .form-select:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    .rounded-pill {
        border-radius: 50rem !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
            <h3 class="mb-0 text-primary">Applications for {{ job.title }}</h3>
            <a href="{% url 'jobs:job_detail' job.pk %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Job
            </a>
        </div>
        
        <div class="card-body">
            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <form method="get" class="d-flex align-items-center gap-3">
                        <div class="form-group">
                            <label class="form-label text-muted mb-1">Status:</label>
                            <select name="status" class="form-select" style="min-width: 200px;" onchange="this.form.submit()">
                                <option value="">All Statuses</option>
                                {% for status_code, status_label in status_choices %}
                                <option value="{{ status_code }}" {% if current_status == status_code %}selected{% endif %}>
                                    {{ status_label }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label text-muted mb-1">Sort by:</label>
                            <select name="sort" class="form-select" style="min-width: 200px;" onchange="this.form.submit()">
                                <option value="-interview_score" {% if current_sort == '-interview_score' %}selected{% endif %}>
                                    Interview Score (High to Low)
                                </option>
                                <option value="-applied_at" {% if current_sort == '-applied_at' %}selected{% endif %}>
                                    Newest First
                                </option>
                                <option value="status" {% if current_sort == 'status' %}selected{% endif %}>
                                    Status
                                </option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Applications Table -->
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="py-3">Applicant</th>
                            <th class="py-3">Applied Date</th>
                            <th class="py-3">Interview Score</th>
                            <th class="py-3">Status</th>
                            <th class="py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for application in applications %}
                        <tr>
                            <td class="py-3">
                                <div class="fw-medium">{{ application.applicant.get_full_name }}</div>
                                <small class="text-muted">{{ application.applicant.email }}</small>
                            </td>
                            <td class="py-3">{{ application.applied_at|date:"M d, Y" }}</td>
                            <td class="py-3">
                                {% if application.interview_score %}
                                    <div class="px-3 py-1 d-inline-block rounded-pill {% if application.interview_score >= 8 %}bg-success-subtle text-success{% elif application.interview_score >= 6 %}bg-info-subtle text-primary{% else %}bg-warning-subtle text-warning{% endif %}">
                                        <strong>{{ application.interview_score }}/10</strong>
                                    </div>
                                {% else %}
                                    <span class="px-3 py-1 rounded-pill bg-secondary-subtle text-secondary">
                                        Pending
                                    </span>
                                {% endif %}
                            </td>
                            <td class="py-3">
                                <span class="px-3 py-1 rounded-pill 
                                    {% if application.status == 'new' %}bg-secondary-subtle text-secondary{% endif %}
                                    {% if application.status == 'reviewing' %}bg-info-subtle text-primary{% endif %}
                                    {% if application.status == 'shortlisted' %}bg-primary-subtle text-primary{% endif %}
                                    {% if application.status == 'interview_scheduled' %}bg-warning-subtle text-warning{% endif %}
                                    {% if application.status == 'interview_completed' %}bg-info-subtle text-primary{% endif %}
                                    {% if application.status == 'offer_made' %}bg-success-subtle text-success{% endif %}
                                    {% if application.status == 'offer_accepted' %}bg-success-subtle text-success{% endif %}
                                    {% if application.status == 'rejected' %}bg-danger-subtle text-danger{% endif %}">
                                    {{ application.get_status_display }}
                                </span>
                            </td>
                            <td class="py-3">
                                <a href="{% url 'jobs:application_detail' application.pk %}" 
                                   class="btn btn-sm btn-primary">
                                    View Details
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center py-4 text-muted">
                                <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                                No applications found
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Analysis Modal -->
<div class="modal fade" id="analysisModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold">Interview Analysis</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="analysisContent" class="markdown-body">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('analysisModal');
    modal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const applicationId = button.getAttribute('data-application-id');
        const analysisContent = document.getElementById('analysisContent');
        
        // Fetch analysis data
        fetch(`/interviews/analysis-data/${applicationId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'completed') {
                    // Convert markdown to HTML
                    const htmlContent = marked.parse(data.analysis);
                    analysisContent.innerHTML = `
                        <div class="analysis-content">
                            ${htmlContent}
                        </div>
                    `;
                } else if (data.status === 'processing') {
                    analysisContent.innerHTML = `
                        <div class="text-center">
                            <div class="spinner-border text-primary"></div>
                            <p class="mt-2">Analysis in progress...</p>
                        </div>
                    `;
                } else {
                    analysisContent.innerHTML = `
                        <div class="alert alert-danger">
                            ${data.error || 'Analysis failed'}
                        </div>
                    `;
                }

                // Add syntax highlighting if needed
                if (typeof hljs !== 'undefined') {
                    document.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightBlock(block);
                    });
                }
            })
            .catch(error => {
                analysisContent.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading analysis: ${error}
                    </div>
                `;
            });
    });
});
</script>
{% endblock %} 