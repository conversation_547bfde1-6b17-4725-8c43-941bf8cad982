<!-- Pending Video Interviews Section -->
{% if pending_interviews %}
<div class="col-12 mb-4">
    <div class="card shadow-sm">
        <div class="card-header bg-warning-subtle">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-video me-2"></i>
                    Pending Video Interviews
                </h5>
                <span class="badge bg-warning text-dark">{{ pending_interviews|length }}</span>
            </div>
        </div>
        <div class="list-group list-group-flush">
            {% for application in pending_interviews %}
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">{{ application.job.title }}</h6>
                        <p class="mb-1 text-muted small">{{ application.job.employer.company_name }}</p>
                        <small class="text-muted">Applied {{ application.applied_at|timesince }} ago</small>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{% url 'interviews:record_response' application.interview_set.first.id %}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-video me-2"></i>Start Interview
                        </a>
                        <button type="button" 
                                class="btn btn-outline-secondary btn-sm" 
                                data-bs-toggle="tooltip" 
                                title="Duration: {{ application.job.interview_duration }} minutes">
                            <i class="fas fa-clock"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %} 