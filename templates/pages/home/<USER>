<section class="how-it-works" data-tabs>
  <div class="site-container">
    <div class="tab-btn-holder">
      <div class="tab-btn-wrapper">
        <button class="tab-btn active" data-tab-button="for-employer">
          <div class="icon-holder">
            <svg width="19" height="19" viewBox="0 0 19 19" fill="none">
              <g clip-path="url(#clip0_54795_2039)">
                <path opacity="0.2" d="M2.98535 3.17505H7.55254V15.7348H2.98535V3.17505ZM12.1197 6.60044V15.7348H16.6869V6.60044H12.1197Z" fill="white"/>
                <path d="M17.8285 15.164H17.2576V6.60054C17.2576 6.44913 17.1975 6.30392 17.0904 6.19686C16.9834 6.08979 16.8381 6.02964 16.6867 6.02964H12.1195C11.9681 6.02964 11.8229 6.08979 11.7159 6.19686C11.6088 6.30392 11.5486 6.44913 11.5486 6.60054V9.45504H8.12325V3.17515C8.12325 3.02374 8.0631 2.87852 7.95604 2.77146C7.84897 2.6644 7.70376 2.60425 7.55235 2.60425H2.98516C2.83375 2.60425 2.68854 2.6644 2.58147 2.77146C2.47441 2.87852 2.41426 3.02374 2.41426 3.17515V15.164H1.84336C1.69195 15.164 1.54674 15.2242 1.43967 15.3312C1.33261 15.4383 1.27246 15.5835 1.27246 15.7349C1.27246 15.8863 1.33261 16.0316 1.43967 16.1386C1.54674 16.2457 1.69195 16.3058 1.84336 16.3058H17.8285C17.9799 16.3058 18.1252 16.2457 18.2322 16.1386C18.3393 16.0316 18.3994 15.8863 18.3994 15.7349C18.3994 15.5835 18.3393 15.4383 18.2322 15.3312C18.1252 15.2242 17.9799 15.164 17.8285 15.164ZM12.6904 7.17144H16.1158V15.164H12.6904V7.17144ZM11.5486 10.5968V15.164H8.12325V10.5968H11.5486ZM3.55606 3.74605H6.98145V15.164H3.55606V3.74605ZM5.83965 5.45874V6.60054C5.83965 6.75195 5.77951 6.89716 5.67244 7.00423C5.56538 7.11129 5.42017 7.17144 5.26875 7.17144C5.11734 7.17144 4.97213 7.11129 4.86507 7.00423C4.758 6.89716 4.69786 6.75195 4.69786 6.60054V5.45874C4.69786 5.30733 4.758 5.16212 4.86507 5.05506C4.97213 4.94799 5.11734 4.88784 5.26875 4.88784C5.42017 4.88784 5.56538 4.94799 5.67244 5.05506C5.77951 5.16212 5.83965 5.30733 5.83965 5.45874ZM5.83965 8.88414V10.0259C5.83965 10.1773 5.77951 10.3226 5.67244 10.4296C5.56538 10.5367 5.42017 10.5968 5.26875 10.5968C5.11734 10.5968 4.97213 10.5367 4.86507 10.4296C4.758 10.3226 4.69786 10.1773 4.69786 10.0259V8.88414C4.69786 8.73273 4.758 8.58752 4.86507 8.48045C4.97213 8.37339 5.11734 8.31324 5.26875 8.31324C5.42017 8.31324 5.56538 8.37339 5.67244 8.48045C5.77951 8.58752 5.83965 8.73273 5.83965 8.88414ZM5.83965 12.3095V13.4513C5.83965 13.6027 5.77951 13.748 5.67244 13.855C5.56538 13.9621 5.42017 14.0222 5.26875 14.0222C5.11734 14.0222 4.97213 13.9621 4.86507 13.855C4.758 13.748 4.69786 13.6027 4.69786 13.4513V12.3095C4.69786 12.1581 4.758 12.0129 4.86507 11.9058C4.97213 11.7988 5.11734 11.7386 5.26875 11.7386C5.42017 11.7386 5.56538 11.7988 5.67244 11.9058C5.77951 12.0129 5.83965 12.1581 5.83965 12.3095ZM9.26505 13.4513V12.3095C9.26505 12.1581 9.3252 12.0129 9.43226 11.9058C9.53933 11.7988 9.68454 11.7386 9.83595 11.7386C9.98736 11.7386 10.1326 11.7988 10.2396 11.9058C10.3467 12.0129 10.4068 12.1581 10.4068 12.3095V13.4513C10.4068 13.6027 10.3467 13.748 10.2396 13.855C10.1326 13.9621 9.98736 14.0222 9.83595 14.0222C9.68454 14.0222 9.53933 13.9621 9.43226 13.855C9.3252 13.748 9.26505 13.6027 9.26505 13.4513ZM13.8322 13.4513V12.3095C13.8322 12.1581 13.8924 12.0129 13.9995 11.9058C14.1065 11.7988 14.2517 11.7386 14.4031 11.7386C14.5546 11.7386 14.6998 11.7988 14.8068 11.9058C14.9139 12.0129 14.974 12.1581 14.974 12.3095V13.4513C14.974 13.6027 14.9139 13.748 14.8068 13.855C14.6998 13.9621 14.5546 14.0222 14.4031 14.0222C14.2517 14.0222 14.1065 13.9621 13.9995 13.855C13.8924 13.748 13.8322 13.6027 13.8322 13.4513ZM13.8322 10.0259V8.88414C13.8322 8.73273 13.8924 8.58752 13.9995 8.48045C14.1065 8.37339 14.2517 8.31324 14.4031 8.31324C14.5546 8.31324 14.6998 8.37339 14.8068 8.48045C14.9139 8.58752 14.974 8.73273 14.974 8.88414V10.0259C14.974 10.1773 14.9139 10.3226 14.8068 10.4296C14.6998 10.5367 14.5546 10.5968 14.4031 10.5968C14.2517 10.5968 14.1065 10.5367 13.9995 10.4296C13.8924 10.3226 13.8322 10.1773 13.8322 10.0259Z" fill="#9CA3AF"/>
              </g>
              <defs>
                <clipPath id="clip0_54795_2039">
                  <rect width="18.2688" height="18.2688" fill="white" transform="translate(0.70166 0.320801)"/>
                </clipPath>
              </defs>
            </svg>  
          </div>
          Employer
        </button>
        <button class="tab-btn" data-tab-button="for-candidate">
          <div class="icon-holder">
            <svg width="19" height="20" viewBox="0 0 19 20" fill="none">
              <path d="M13.9185 8.17153C13.9185 9.07483 13.6506 9.95785 13.1488 10.7089C12.6469 11.46 11.9336 12.0454 11.0991 12.3911C10.2645 12.7367 9.34622 12.8272 8.46028 12.651C7.57433 12.4747 6.76053 12.0398 6.1218 11.401C5.48306 10.7623 5.04808 9.94849 4.87186 9.06254C4.69563 8.17659 4.78607 7.25828 5.13175 6.42374C5.47743 5.58919 6.06282 4.87589 6.81389 4.37404C7.56497 3.87219 8.44799 3.60433 9.35129 3.60433C10.5626 3.60433 11.7243 4.08552 12.5808 4.94203C13.4373 5.79855 13.9185 6.96023 13.9185 8.17153Z" fill="#9CA3AF" fill-opacity="0.2"/>
              <path d="M16.6958 16.4496C15.609 14.5706 13.9341 13.2233 11.9795 12.5846C12.9463 12.009 13.6975 11.132 14.1177 10.0882C14.5378 9.04437 14.6037 7.8915 14.3052 6.80662C14.0068 5.72174 13.3604 4.76483 12.4655 4.08284C11.5705 3.40085 10.4764 3.03149 9.35121 3.03149C8.22602 3.03149 7.13194 3.40085 6.23698 4.08284C5.34202 4.76483 4.69567 5.72174 4.39719 6.80662C4.09872 7.8915 4.16462 9.04437 4.58477 10.0882C5.00492 11.132 5.7561 12.009 6.72294 12.5846C4.76832 13.2226 3.09345 14.5699 2.0066 16.4496C1.96674 16.5146 1.9403 16.5869 1.92885 16.6622C1.91739 16.7376 1.92114 16.8145 1.93989 16.8884C1.95864 16.9623 1.99199 17.0317 2.03799 17.0925C2.08399 17.1533 2.1417 17.2043 2.20771 17.2424C2.27373 17.2805 2.34671 17.305 2.42236 17.3145C2.49801 17.324 2.57478 17.3182 2.64816 17.2975C2.72154 17.2768 2.79003 17.2417 2.84959 17.1941C2.90916 17.1465 2.95859 17.0875 2.99497 17.0205C4.33943 14.6969 6.7158 13.3096 9.35121 13.3096C11.9866 13.3096 14.363 14.6969 15.7075 17.0205C15.7438 17.0875 15.7933 17.1465 15.8528 17.1941C15.9124 17.2417 15.9809 17.2768 16.0543 17.2975C16.1276 17.3182 16.2044 17.324 16.2801 17.3145C16.3557 17.305 16.4287 17.2805 16.4947 17.2424C16.5607 17.2043 16.6184 17.1533 16.6644 17.0925C16.7104 17.0317 16.7438 16.9623 16.7625 16.8884C16.7813 16.8145 16.785 16.7376 16.7736 16.6622C16.7621 16.5869 16.7357 16.5146 16.6958 16.4496ZM5.35492 8.17153C5.35492 7.38114 5.5893 6.60849 6.02842 5.95131C6.46753 5.29412 7.09167 4.7819 7.8219 4.47943C8.55213 4.17696 9.35565 4.09782 10.1309 4.25202C10.9061 4.40622 11.6181 4.78683 12.177 5.34572C12.7359 5.90461 13.1165 6.61668 13.2707 7.39189C13.4249 8.1671 13.3458 8.97062 13.0433 9.70084C12.7408 10.4311 12.2286 11.0552 11.5714 11.4943C10.9142 11.9334 10.1416 12.1678 9.35121 12.1678C8.29168 12.1667 7.27586 11.7453 6.52666 10.9961C5.77745 10.2469 5.35605 9.23106 5.35492 8.17153Z" fill="#9CA3AF"/>
            </svg>
          </div>
          Candidate
        </button>
      </div>
    </div>
    <div class="tab-content">
      <div class="tab-content__pane tab-content__pane--active tab-content__pane--fade-in" data-tab-pane="for-employer">
        <div class="tab-content__right">
          <div class="tab-content__img-holder">
            <img src="../../../static/images/img-1.jpg">
          </div>
        </div>
        <div class="tab-content__left">
          <div class="tab-content__title-holder">
            <span class="tab-content__sub-title">For Employers</span>
            <h2 class="tab-content__title">How It Works</h2>
          </div>
          <div class="list">
            <div class="list__item">
              <h3 class="list__title">
                <span class="list__title--text-large">01</span> Post Your Role
              </h3>
              <p class="list__description">Share the opportunity and add role-specific questions.</p>
            </div>

            <div class="list__item">
              <h3 class="list__title">
                <span class="list__title--text-large">02</span> Get Video Applications
              </h3>
              <p class="list__description">Candidates respond with 2–3 minute video intros that showcase their communication skills, confidence, and presence.</p>
            </div>

            <div class="list__item">
              <h3 class="list__title">
                <span class="list__title--text-large">03</span> Receive Smart Summaries
              </h3>
              <p class="list__description">Vouch uses AI to transcribe, analyze, and summarize responses—so you can compare top applicants instantly.</p>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content__pane" data-tab-pane="for-candidate">
        <div class="tab-content__right">
          <div class="tab-content__img-holder">
            <img src="../../../static/images/img-2.jpg">
          </div>
        </div>
        <div class="tab-content__left">
          <div class="tab-content__title-holder">
            <span class="tab-content__sub-title">For Candidates</span>
            <h2 class="tab-content__title">How It Works</h2>
          </div>
          <div class="list">
            <div class="list__item">
              <h3 class="list__title">
                <span class="list__title--text-large">01</span> See Open Roles
              </h3>
              <p class="list__description">Find positions that fit your voice—not just your resume.</p>
            </div>

            <div class="list__item">
              <h3 class="list__title">
                <span class="list__title--text-large">02</span> Record Your Vouch
              </h3>
              <p class="list__description">Answer questions or introduce yourself in 2–3 minutes.</p>
            </div>

            <div class="list__item">
              <h3 class="list__title">
                <span class="list__title--text-large">03</span> Stand Out—For Real
              </h3>
              <p class="list__description">Let your communication, energy, and personality shine through.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>